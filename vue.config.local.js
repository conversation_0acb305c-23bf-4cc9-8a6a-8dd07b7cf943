const { rule } = require("postcss");

/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-11-01 16:55:13
 * @LastEditors: chenwei<PERSON>
 * @Description: 
 * @LastEditTime: 2022-11-08 14:29:44
 */
module.exports = {
  publicPath: '.',
  devServer: {
    disableHostCheck: true,
	port: 8080,
    proxy: {
      '/api': {
        // 测试服
        target: 'http://localhost:8090',
        // 正式服
        // target:'https://profitup-admin-api.3g.cn',
        changeOrigin: true,
        logLeve:'debug',
        onProxyReq:function (proxyReq, req, res, options) {
          if (req.body) {
            let bodyData = JSON.stringify(req.body);
            // incase if content-type is application/x-www-form-urlencoded -> we need to change to application/json
            proxyReq.setHeader('Content-Type','application/json');
            proxyReq.setHeader('Content-Length', Buffer.byteLength(bodyData));
            // stream the content
            proxyReq.write(bodyData);
          }
        },
        pathRewrite: {
          '^/api': '/'
        }
      }
    }
  }
}