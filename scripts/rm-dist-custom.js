#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// 日志函数，添加特色前缀
function log(message) {
  console.log(`🧹【清理工具】${message}`);
}

function errorLog(message) {
  console.error(`❌【清理工具】${message}`);
}

// 获取PROJECT_NAME环境变量
const projectName = process.env.PROJECT_NAME;

if (!projectName) {
  errorLog('请设置 PROJECT_NAME 环境变量');
  process.exit(1);
}

const imgDir = path.resolve(__dirname, '../dist/img');

// 检查dist/img目录是否存在
if (!fs.existsSync(imgDir)) {
  errorLog('dist/img 目录不存在');
  process.exit(1);
}

// 获取dist/img下所有文件/目录
const files = fs.readdirSync(imgDir);

// 要保留的目录名
const keepDirs = [`img_public`, `img_${projectName}`];

log(`保留目录: ${keepDirs.join(', ')}`);
log(`开始清理多余目录...`);

// 遍历并移除不需要保留的目录
let removedCount = 0;
for (const file of files) {
  const filePath = path.join(imgDir, file);
  const stat = fs.statSync(filePath);
  
  if (stat.isDirectory() && !keepDirs.includes(file)) {
    log(`移除: ${file}`);
    fs.rmSync(filePath, { recursive: true, force: true });
    removedCount++;
  }
}

log(`✅ 清理完成! 共移除 ${removedCount} 个目录`); 