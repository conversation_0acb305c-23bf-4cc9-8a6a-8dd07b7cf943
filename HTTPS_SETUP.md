# HTTPS 本地开发环境配置

本项目已配置使用 mkcert 生成的本地受信任证书来支持 HTTPS 开发环境。

## 配置说明

### 证书文件
- 证书位置: `certs/localhost+2.pem`
- 私钥位置: `certs/localhost+2-key.pem`
- 支持的域名:
  - localhost
  - 127.0.0.1
  - linzengrui-dev.unbing.cn

### Vue.js 配置
在 `vue.config.js` 中已配置 HTTPS:

```javascript
devServer: {
  host: '0.0.0.0',
  https: {
    key: fs.readFileSync(path.resolve(__dirname, 'certs/localhost+2-key.pem')),
    cert: fs.readFileSync(path.resolve(__dirname, 'certs/localhost+2.pem')),
  },
  // ... 其他配置
}
```

## 使用方法

### 启动开发服务器
```bash
npm run dev
# 或
yarn dev
```

### 访问地址
- https://localhost:8080
- https://127.0.0.1:8080
- https://linzengrui-dev.unbing.cn:8080

## 证书管理

### 重新生成证书
如果需要重新生成证书（比如添加新的域名），可以执行：

```bash
cd certs
mkcert localhost 127.0.0.1 linzengrui-dev.unbing.cn [新域名]
```

### 证书有效期
当前证书有效期至: **2027年10月31日**

### 添加新域名
如果需要支持新的域名，需要：

1. 重新生成证书包含新域名
2. 更新 `vue.config.js` 中的 `allowedHosts` 配置

## 故障排除

### 证书不受信任
如果浏览器显示证书不受信任，请确保：
1. mkcert 的本地 CA 已正确安装: `mkcert -install`
2. 重启浏览器

### 无法访问 HTTPS
1. 确保证书文件存在于 `certs/` 目录
2. 检查文件权限
3. 确保端口 8080 未被占用

### 热更新问题
如果遇到热更新问题，请检查：
1. `public` 配置是否正确
2. `allowedHosts` 是否包含访问的域名
3. 防火墙是否阻止了连接

## 注意事项

1. 证书文件不应提交到版本控制系统
2. 每台开发机器都需要单独安装 mkcert 和生成证书
3. 生产环境应使用正式的 SSL 证书
