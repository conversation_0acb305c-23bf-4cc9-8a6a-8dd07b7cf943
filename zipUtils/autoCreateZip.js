const { exec, execSync,  } = require('child_process')
const packgeJson = require('../package.json')
/**
流程：
条件：
    一定得存在打包文件 dist  与  可能有压缩文件夹 dist.zip
代码流程：
    1. 删除旧的 dist.zip
    2. 将 dist 文件夹 压缩成新的 dist.zip
 */
const fs = require('fs')
// https://github.com/archiverjs/node-archiver/
const archiver = require('archiver')
const path = require('path')
// __dirname 是获取当前
// 如果是在 packge.json 运行，参数就需要用 ./ 如果是测试就需要 ../
const currentDirname = path.resolve('./')

let currentDate = new Date()
// 得出 2023_11_29__19_58_45
let time = currentDate.toLocaleString().replace(/\//g, '_').replace(/\:/g, '_').replace(/ /g, '__')
const zipName =  `/${process.env.PROJECT_NAME}_${packgeJson.name}_${process.env.PROJECT_ENV}_${time}`
const dirName = '/dist' 

// 讲 dist 压缩成 dist.zip
const fileToZip = async () => {
    let argv = process.argv
    let [ 
        , 
        resultPath,  // 当前打包后的路径
        otherParams, // 打包额外名字  标识
    ] = argv

    
    let outName = currentDirname + zipName
    if (otherParams) { // 额外的打包标识
        try {
            let e = execSync('git rev-parse --abbrev-ref HEAD')
            e = e.toString().trim()
            console.log("🚀 ~ fileToZip ~ e:", e)
            outName += `_${e}`
        } catch (error) {
            console.error(`当前分支名称ERROR: ${error}`)
        }
        outName += `_${otherParams}`
    }

    // 移除其他不相干的图片目录
    const targetDirectories = [
        'img_public', // 固定保留
        `img_${process.env.PROJECT_NAME}`,
    ] // 包含需要保留的目录名的数组
    const sourceDirectory = `${currentDirname + dirName}/img` // 源目录
    console.log("🚀 ~ fileToZip ~ sourceDirectory:", sourceDirectory)
    console.log("🚀 ~ fileToZip ~ targetDirectories:", targetDirectories)
    await rmImageDir(sourceDirectory, targetDirectories)

    outName += `.zip`
    let output = fs.createWriteStream(outName)
    console.log("🚀 ~ 生成压缩文件:", outName)
    console.log(` `);

    const archive = archiver('zip', {
        zlib: { level: 9 }, // Sets the compression level.
    })

    //监听所有要写入的归档数据
    //只有涉及到文件描述符时才触发'close'事件
    output.on('close', function() {
        console.log(archive.pointer() + ' total bytes');
        console.log('archiver has been finalized and the output file descriptor has closed.')
    })

    //无论数据源是什么，当数据源耗尽时都会触发此事件。
    //它不是这个库的一部分，而是来自NodeJS流API。
    // @see: https://nodejs.org/api/stream.html#stream_event_end
    output.on('end', function() {
        console.log('Data has been drained')
    }) 

    archive.on('warning', function(err) {
        if (err.code === 'ENOENT') {
            // log warning
        } else {
            // throw error
            throw err
        }
    })
    
    archive.on('error', function(err) {
        throw err
    })

    // pipe archive data to the file
    archive.pipe(output)

    // append a file from stream
    // const file1 = currentDirname + '/2.txt'
    // archive.append(fs.createReadStream(file1), { name: '2.txt' })

    // 把整个文件压缩
    let y = currentDirname + dirName
    archive.directory(y, 'dist')

    // 完成
    archive.finalize()

    // 打开当前目录
    execSync('open ./')
}

//  移除所有zip压缩包
const removeAllZip = () => {
    const files = fs.readdirSync(currentDirname)
    for (const f of files) {
        let isZip = f.endsWith('zip')
        if (isZip) {
            let result = currentDirname + '/' + f
            fs.unlinkSync(result)
            console.log(`🚀 ~ 移除压缩文件: ${result} 成功`,)
            console.log(` `,)
        }
    }
    // console.log("🚀 ~ 获取当前目录下所有文件:", files)
}

/**
 * 移除图片目录
 * @param {*} sourceDirectory           检测的路径
 * @param {*} targetDirectories          保留的目录
 */
const rmImageDir = (sourceDirectory, targetDirectories) => {
    return new Promise((resolve, reject) => {
        fs.readdir(sourceDirectory, async (err, files) => {
            if (err) {
                reject(err)
                return
            }

            try {
                for (const file of files) {
                    const sourcePath = `${sourceDirectory}/${file}`
                    const stats = await fs.promises.stat(sourcePath)

                    if (stats.isDirectory()) {
                        if (targetDirectories.includes(file)) {
                            // console.log(`已保留目录: ${sourcePath}`)
                        } else {
                            await fs.promises.rmdir(sourcePath, {
                                recursive: true,
                            })
                            console.log(`已删除 图片 目录: ${sourcePath}`)
                        }
                    }
                }
                resolve()
            } catch (error) {
                reject(error)
            }
        })
    })
}

const __main = () => {
    // 获取当前目录下的 所有文件
    removeAllZip()
    // 将 dist 压缩成 zip 文件
    fileToZip()
}

// console.log(`output->currentDirname`, currentDirname)
__main()