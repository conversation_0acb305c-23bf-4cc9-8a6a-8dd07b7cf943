<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-11-30 14:44:06
 * @LastEditors: chen<PERSON><PERSON>
 * @Description: 
 * @LastEditTime: 2022-12-12 11:27:15
-->
## 模板使用指南

### 使用说明
本开发模板适用于app内嵌活动页

### 适配
为了适配移动端，使用了postcss-px-to-viewport，尺寸统一使用750，页面布局时注意UI蓝湖的尺寸是不是已经调到750，直接使用蓝湖像素尺寸即可；但一些占满横屏或者是竖屏的尺寸尽量直接使用100vw以及100vh,在有外层元素包裹时使用100%。postcss配置在postcss.config.js下。

### 样式
公共样式统一放在styles然后在main.js主文件里引入，为了防止样式的覆盖重叠，单文件里需要使用scoped，ui框架的全局需改需要在styles里面的ui.scss里进行覆盖修改。如果是局部的ui框架修改则需要在单文件里面使用样式穿透进行修改。

### 组件封装
组件的封装全部放在components下，但项目较为复杂时可以分为components(样式组件)以及bussiness(业务组件),通过样式相关和业务强相关进行组件的分割。在封装组件时样式上必须只能影响本组件，组件的外间距以及组件的定位(一些悬浮按钮之类的定位除外)需由父组件来定。组件需要遵循单向数据流，对于数据的修改需通过事件或者自定义v-model来实现。

### 路由使用
路由一般简单情况直接在/router/index.js下定义即可，路由较多的情况可按业务需求切分模块。路由守卫方面，全局的守卫逻辑不复杂的情况可在/router/index.js下，复杂的情况需要分一个单独的文件专门做全局守卫，如果需要单独某个路由要做守卫的需要在组件内部使用组件路由钩子做守卫

### store使用

store用来存放全局业务数据，如果是需要请求的数据需要在action里编写，返回promise，同步操作在mutations里编写。


### 压缩图片
绕开熊猫限制压缩图片
在命令行进入到你想要压缩图片的目录，执行：
```
cd src
cd img
super-tinypng
```

### 开发环境
开发环境一般分为三个，分别是本地，测试机以及正式线上，对应的环境变量需要在.env文件内定义，三个环境对应启动以及打包的命令各不相同，如下：
```bash
// 本地启动
npm run serve

// 测试机打包
npm run build:testing

// 线上打包
npm run build
```
测试机部署以及正式部署需要使用阿里云工具进行部署，oss-rel文件以及oss-test文件分别对应正式以及正式服以及测试服，需要询问运维拿到项目对应的key以及桶名等填入到相应的配置文件内

### 部署测试
ossutil64.exe --config-file ../oss-test cp -r ../dist oss://osscdn-sz-unbing-cn/test/  -f


国内调试需在路由加参数testSecretKey=kvBvawSSWrntdmZp
    例如：http://localhost:8080/#/bookDetail?testSecretKey=kvBvawSSWrntdmZp
