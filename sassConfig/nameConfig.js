import mainUtils from '@/utils/mainUtils.js'

// 项目名
const nameSelects = {
    magfic: 'magfic',
    yestory: 'yestory',
    goodfic: 'goodfic',
    booktok: 'booktok',
    novelaria: 'novelaria',
    netfic: 'netfic',
}

// 环境
const envSelect = {
    // 开发
    dev: 'dev',
    // 测试
    local: 'local',
    // 海外开发测试
    overseas: 'dev-overseas',
    // 正式环境
    production: 'production',
}

// 运行脚本
const scriptSelect = {
    // 代码开发中
    devScript: 'devScript',
    // 打包后
    buildScript: 'buildScript',
}

// 项目名 和 环境名 切换
let currentModleName = nameSelects[process.env.PROJECT_NAME] || nameSelects.magfic
    // currentModleName = nameSelects.goodfic
    // currentModleName = nameSelects.yestory
    // currentModleName = nameSelects.novelaria
    // currentModleName = nameSelects.magfic
    // currentModleName = nameSelects.booktok
    // currentModleName = nameSelects.netfic
let currentModleEnv = envSelect[process.env.PROJECT_ENV] || envSelect.overseas
let currentModelScript = scriptSelect[process.env.PROJECT_SCRIPT] || scriptSelect.devScript

// 方便切换环境 
const s1 = nameSelects[mainUtils.debugerQueryCheck('当前sass项目')]
const s2 = envSelect[mainUtils.debugerQueryCheck('当前sass环境')]
if (s1) {
    currentModleName = s1
}
if (s2) {
    currentModleEnv = s2
}

// 当前项目
const isMagficModel = currentModleName === nameSelects.magfic 
const isYestoryModel = currentModleName === nameSelects.yestory
const isGoodficModel = currentModleName === nameSelects.goodfic
const isBooktokModel = currentModleName === nameSelects.booktok
const isNovelariaModel = currentModleName === nameSelects.novelaria
const isNetficModel = currentModleName === nameSelects.netfic
// 当前环境
const isDevEnv = currentModleEnv === envSelect.dev 
const isLocalEnv = currentModleEnv === envSelect.local
const isOverseasEnv = currentModleEnv === envSelect.overseas
const isProductionEnv = currentModleEnv === envSelect.production 

// 开发模式
const isDevScript =   currentModelScript === scriptSelect.devScript
const isBuildScript = currentModelScript === scriptSelect.buildScript 

// 只需要 magfic_webview 的 server path 配置
const uploadSeverPath = {
    default: '',
    // 
    magfic_web: '/magfic_web/',
    yestory_web: '/yestory_web/',
    booktok_web: '/booktok_web/',
}

// 当前 server path
const currentUploadSeverPath = uploadSeverPath[process.env.PROJECT_SERVER_PATH] || uploadSeverPath.default
console.log("🚀 ~ currentUploadSeverPath:", currentUploadSeverPath)

export {
    isMagficModel,
    isYestoryModel,
    isGoodficModel,
    isNovelariaModel,
    isBooktokModel,
    isNetficModel,

    isDevEnv,
    isLocalEnv,
    isOverseasEnv,
    isProductionEnv,

    isDevScript,
    isBuildScript,

    nameSelects,
    envSelect,

    currentModelScript,
    currentModleName,
    currentModleEnv,

    currentUploadSeverPath,
}