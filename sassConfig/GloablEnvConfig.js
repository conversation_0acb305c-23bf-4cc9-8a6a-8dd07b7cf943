// promotion_react_h5
import mainUtils from '@/utils/mainUtils.js'
import {
    isMagficModel,
    isYestoryModel,
    isGoodficModel,
    isNovelariaModel,
    isBooktokModel,
    isNetficModel,

    isDevEnv,
    isLocalEnv,
    isOverseasEnv,
    isProductionEnv,
    
    isDevScript,
    isBuildScript,

    currentModelScript,
    currentModleName,
    currentModleEnv,
    currentUploadSeverPath,
} from './nameConfig.js'


// 公共数据
const GloablEnvConfig = {
    currentModelScript,
    currentModleName,
    currentModleEnv,
    isDevScript,
    isBuildScript,
}


// ajajx 加密
GloablEnvConfig.VUE_APP_ACCESS_KEY =  `4Vv4g1VEEF1ygFYZqIEv`, 
// ajax 解密
GloablEnvConfig.VUE_APP_AES_KEY = `qiVoCJorYWMttbQW`,
// 版本
GloablEnvConfig.VUE_APP_Version = '2.1.0'

// 归因
if (isMagficModel) {
    GloablEnvConfig.oneLinkURL = "https://magfic.onelink.me/oxJU"
} else if (isGoodficModel) {
    GloablEnvConfig.oneLinkURL = "https://goodfic.onelink.me/OVY6"
} else if (isYestoryModel) {
    GloablEnvConfig.oneLinkURL = "https://yestory.onelink.me/Betn"
} else if (isNovelariaModel) {
    GloablEnvConfig.oneLinkURL = "https://novelaria.onelink.me/lnrQ"
} else if (isBooktokModel) {
    GloablEnvConfig.oneLinkURL = "https://booktok.onelink.me/9Ahc"
} else if (isNetficModel) {
    GloablEnvConfig.oneLinkURL = "https://netfic.onelink.me/hXE8"
} 
else {
    alert(`${currentModleName}未配置 oneLinkURL`)
}


// 数数的初始化配置url
if (isMagficModel) {
    GloablEnvConfig.VUE_APP_THINKDATA_SERVER_URL = `https://tacollect.magfic.com`
} else if (isGoodficModel) {
    GloablEnvConfig.VUE_APP_THINKDATA_SERVER_URL = `https://tacollect.goodfic.com`
} else if (isYestoryModel) {
    GloablEnvConfig.VUE_APP_THINKDATA_SERVER_URL = `https://tacollect.yestory.cc`
} else if (isNovelariaModel) {
    GloablEnvConfig.VUE_APP_THINKDATA_SERVER_URL = `https://tacollect.novelaria.net`
} else if (isBooktokModel) {
    GloablEnvConfig.VUE_APP_THINKDATA_SERVER_URL = `https://tacollect.ibooktok.com`
} else if (isNetficModel) {
    GloablEnvConfig.VUE_APP_THINKDATA_SERVER_URL = `https://tacollect.netfic.net`
} 
else {
    alert(`${currentModleName}未配置 VUE_APP_THINKDATA_SERVER_URL`)
}

// 数数
GloablEnvConfig.VUE_APP_THINKDATA_APP_ID_JSON = [
    {
        packgeName: `com.magfic.novel.buu`,
        thinkDataAppId: `843dd1be606d43c8a271d9ef02801b0d`,
        os: `android`
    },
    {
        packgeName: `com.magfic.buu`,
        thinkDataAppId: `406323be4c1140f294ce60ead175c173`,
        os: `ios`,
        appleId: `1621027852`
    },
    {},
    {},
    {
        packgeName: `com.yestory.cc.ebook`,
        thinkDataAppId: `3e09782a75a844318a46070b71f07a87`,
        os: `android`
    },
    {
        packgeName: `com.yestory.cc`,
        thinkDataAppId: `94ad2120e1a14c69addb82de3b966cc0`,
        os: `ios`,
        appleId: `6443988199`
    },
    {},
    {},
    {
        packgeName: `com.goodfic.novel`,
        thinkDataAppId: `8188a0612d154f878655b4e58564241a`,
        os: `android`
    },
    {},
    {},
    {
        // 测试
        packgeName: `com.ibooktok.test`,
        thinkDataAppId: `6476ea23affa40eba69521b8ebbc3d59`, // TODO: 问
        os: `android`
    },
    {
        // 正式
        packgeName: `com.ibooktok.novel`,
        thinkDataAppId: `a6b9ae02ef7640cb9f6f06ae9dd06f03`, // TODO: 问
        os: `android`,
    },
    {},
    {},
    {
        packgeName: `com.novelaria.reader`,
        thinkDataAppId: `5c1e8929f4c54d2f96bba7bce3319456`,
        os: `android`
    },
    {
        packgeName: `com.novelaria.book`,
        thinkDataAppId: `a578a931dc204fb4bf05a6d7ab832a42`,
        os: `ios`,
        appleId: `6587560745`
    },
    {},
    {
        packgeName: `com.netfic.aapp`,
        thinkDataAppId: `c182d9fcf1e945499253d29b9665bbe2`,
        os: `android`
    },
    {
        packgeName: `com.netfic.iapp`,
        thinkDataAppId: `0a834f383a744b939ede5c5722e9a53d`,
        os: `ios`,
        appleId: `6743437405`
    },
    {},
    {},
]
GloablEnvConfig.VUE_APP_THINKDATA_APP_ID_JSON  = JSON.stringify(GloablEnvConfig.VUE_APP_THINKDATA_APP_ID_JSON )




// axios 地址
if (isProductionEnv) {
    GloablEnvConfig.NODE_ENV = 'production'
    GloablEnvConfig.VUE_APP_BASE_URL =  `http://47.90.207.66`
    // GloablEnvConfig.VUE_APP_BASE_URL =  `http://api.magfic.com`
} else if (isDevEnv) {
    GloablEnvConfig.NODE_ENV = 'dev'
    GloablEnvConfig.VUE_APP_BASE_URL =  `http://dev.cms.unbing.cn`
} else if (isLocalEnv) {
    GloablEnvConfig.NODE_ENV = 'local'
    GloablEnvConfig.VUE_APP_BASE_URL =  `https://test-${currentModleName}-web-us.unbing.cn`    
} else if (isOverseasEnv) {
    GloablEnvConfig.NODE_ENV = 'dev-overseas'
    GloablEnvConfig.VUE_APP_BASE_URL =  `http://test-api-us.unbing.cn/`
}
// 打包的都是已根目录访问api
if (isBuildScript) {
    GloablEnvConfig.VUE_APP_BASE_URL = '/'
    console.log(`log 调试数据: VUE_APP_BASE_URL路径是根目录`);
} 

// TODO  
// 1应该拆分为俩个，
//      一个是 h5与webview相同的一些共同的配置
//      一个是各自项目自己拥有的配置数据
// 2 目前的代码都是 if else 是不应该的，应该区分 项目名.json 文件


// 推广页
// 包名
if (isProductionEnv) {
    GloablEnvConfig.VUE_APP_PACKAGENAME = `com.${currentModleName}.web`       // com.magfic.web 
} else {
    GloablEnvConfig.VUE_APP_PACKAGENAME = `com.${currentModleName}.web.test` //  com.magfic.web.test
}

// facebook meta 的 content 到时候直接加上加好了
// 访问facebook域名白名单的密钥
if (isDevEnv || isLocalEnv) {
    GloablEnvConfig.VUE_APP_FACEBOOK_VERIFICATION_KEY = ``
} else {
    if (isProductionEnv) {
        if (isMagficModel) {
            GloablEnvConfig.VUE_APP_FACEBOOK_VERIFICATION_KEY = `zy7d9ulvs2bcdumh7knoiq80rh2co7`
        } else if (isYestoryModel) {
            GloablEnvConfig.VUE_APP_FACEBOOK_VERIFICATION_KEY = `o5swz1gfl4vb1yaxowvs2fg6q7pvvd`
        } else if (isGoodficModel) {
            GloablEnvConfig.VUE_APP_FACEBOOK_VERIFICATION_KEY = `2t4p86iiz1x0zs3b7l7qoc39vz7ur8`
        } else if (isNovelariaModel) {
            GloablEnvConfig.VUE_APP_FACEBOOK_VERIFICATION_KEY = ``
        } else if (isBooktokModel) {
            // TODO: 问
        }
    } else if (isOverseasEnv) {
        if (isMagficModel) {
            GloablEnvConfig.VUE_APP_FACEBOOK_VERIFICATION_KEY = `bufev5x249q4ttq9vpjqe8jx6mkvvr`
        } else if (isYestoryModel) {
            GloablEnvConfig.VUE_APP_FACEBOOK_VERIFICATION_KEY = `wzym2jy3fxowv75udf8ca1ost5qt3q`
        } else if (isGoodficModel) {
            GloablEnvConfig.VUE_APP_FACEBOOK_VERIFICATION_KEY = `rkj3kki0s5gfzm055ghkoqvlict0gr`
        } else if (isNovelariaModel) {
            GloablEnvConfig.VUE_APP_FACEBOOK_VERIFICATION_KEY = ``
        } else if (isBooktokModel) {
            // TODO: 问
        }
    }
}
let attributtionURL = GloablEnvConfig.VUE_APP_BASE_URL
if (currentUploadSeverPath) {
    // attributtionURL = currentUploadSeverPath.slice(0, -1) // 原本是 /a/ 去掉最后一个 /
}
// 上报归因结果
GloablEnvConfig.VUE_APP_CLICK_IP_ATTRIBUTION_DOMAIN = `${attributtionURL}verifysource_api/ISO1818001`  // https://test-magfic-web-us.unbing.cn/verifysource_api/ISO1818001
// 不知道什么鬼东西 
// if(isProductionEnv) {
//     GloablEnvConfig.VUE_APP_CLICK_IP_ATTRIBUTION_DOMAIN = `/verifysource_api/ISO1818001`
// } else if(isLocalEnv) {
//     GloablEnvConfig.VUE_APP_CLICK_IP_ATTRIBUTION_DOMAIN = `https://test-${currentModleName}-web-us.unbing.cn/verifysource_api/ISO1818001` // https://test-magfic-web-us.unbing.cn/verifysource_api/ISO1818001
// } else if(isDevEnv) {
//     GloablEnvConfig.VUE_APP_CLICK_IP_ATTRIBUTION_DOMAIN = `http://dev.cms.unbing.cn/verifysource_api/ISO1818001`
// } else if(isOverseasEnv) {
//     GloablEnvConfig.VUE_APP_CLICK_IP_ATTRIBUTION_DOMAIN = `/verifysource_api/ISO1818001`
// }


if (isProductionEnv) {
// 正式环境
    if (isMagficModel) {
        GloablEnvConfig.gtagJSinitId = `G-WW6R52YHZK`
    } else if (isYestoryModel) {
        // TODO: 问
    } else if (isGoodficModel) {
        // TODO: 问
    } else if (isNovelariaModel) {
        // TODO: 问
    } else if (isBooktokModel) {
        // TODO: 问
    } else if (isNetficModel) {
        // TODO: 问
    }
} else {
    if (isMagficModel) {
        GloablEnvConfig.gtagJSinitId = `G-SMZ3WFM2YW`
    } else if (isYestoryModel) {
        // TODO: 问
    } else if (isGoodficModel) {
        // TODO: 问
    } else if (isNovelariaModel) {
        // TODO: 问
    } else if (isBooktokModel) {
        // TODO: 问
    } else if (isNetficModel) {
        // TODO: 问
    }
}

// 归因 af 脚本 (旧)
// if (isMagficModel) {
//     GloablEnvConfig.FACEBOOK_FILENAME = `utils/onelinkFacebookOrTiktok/public__onelink-smart-script-latest.js`
//     GloablEnvConfig.TIKTOK_FILENAME = `utils/onelinkFacebookOrTiktok/public__onelink-smart-script-latest.js`
//     GloablEnvConfig.EMAIL_FILENAME = `utils/onelinkEmail/email__onelink-smart-script-latest.js`
// } else if (isYestoryModel) {
//     GloablEnvConfig.FACEBOOK_FILENAME = `utils/onelinkFacebookOrTiktok/public__onelink-smart-script-latest.js`
//     GloablEnvConfig.TIKTOK_FILENAME = `utils/onelinkFacebookOrTiktok/public__onelink-smart-script-latest.js`
// } else if (isGoodficModel) {
//     GloablEnvConfig.FACEBOOK_FILENAME = `utils/onelinkFacebookOrTiktok/public__onelink-smart-script-latest.js`
//     GloablEnvConfig.TIKTOK_FILENAME = `utils/onelinkFacebookOrTiktok/public__onelink-smart-script-latest.js`
// } else if (isNovelariaModel) {
//     GloablEnvConfig.FACEBOOK_FILENAME = `utils/onelinkFacebookOrTiktok/public__onelink-smart-script-latest.js`
//     GloablEnvConfig.TIKTOK_FILENAME = `utils/onelinkFacebookOrTiktok/public__onelink-smart-script-latest.js`
// } else if (isBooktokModel) {
//     GloablEnvConfig.FACEBOOK_FILENAME = `utils/onelinkFacebookOrTiktok/public__onelink-smart-script-latest.js`
// } else if (isNetficModel) {
//     GloablEnvConfig.FACEBOOK_FILENAME = `utils/onelinkFacebookOrTiktok/public__onelink-smart-script-latest.js`
// }
// else {
//     alert(`${currentModleName}未配置 FACEBOOK_FILENAME`)
// }

// Snapchat 脚本
GloablEnvConfig.FACEBOOK_FILENAME = `utils/onelinkFacebookOrTiktok/public__onelink-smart-script-latest.js`
GloablEnvConfig.TIKTOK_FILENAME = `utils/onelinkFacebookOrTiktok/public__onelink-smart-script-latest.js`
GloablEnvConfig.SNAPCHAT_FILENAME = `utils/onelinkFacebookOrTiktok/public__onelink-smart-script-latest.js`
GloablEnvConfig.PINTEREST_FILENAME = `utils/onelinkFacebookOrTiktok/public__onelink-smart-script-latest.js`
GloablEnvConfig.EMAIL_FILENAME = `utils/onelinkEmail/email__onelink-smart-script-latest.js` // 暂时用不到的

// 广告 ads pixel id 
if (isMagficModel) {
    GloablEnvConfig.DEFAULT_FACEBOOK_PIXELID = `200075699619404`
    GloablEnvConfig.DEFAULT_TIKTOK_PIXELID = `CNBBV83C77U00VSBQ7DG`
    GloablEnvConfig.DEFAULT_SNAPCHAT_PIXELID = `还没有，到时候问产品`
    GloablEnvConfig.DEFAULT_PINTEREST_PIXELID = `2612451981052`
} else if (isYestoryModel) {
    GloablEnvConfig.DEFAULT_FACEBOOK_PIXELID = `688287449978340`
    GloablEnvConfig.DEFAULT_TIKTOK_PIXELID = `CNK5KBBC77U1S7IC04LG`
    GloablEnvConfig.DEFAULT_SNAPCHAT_PIXELID = `4f38757b-41e1-4918-a609-439e73c6aa78`
    GloablEnvConfig.DEFAULT_PINTEREST_PIXELID = ``
} else if (isGoodficModel) {
    GloablEnvConfig.DEFAULT_FACEBOOK_PIXELID = `691153719015980`
    GloablEnvConfig.DEFAULT_TIKTOK_PIXELID = `CQG70MJC77U4CIR2S7QG`
    GloablEnvConfig.DEFAULT_SNAPCHAT_PIXELID = `还没有，到时候问产品`
    GloablEnvConfig.DEFAULT_PINTEREST_PIXELID = ``
} else if (isNovelariaModel) {
    GloablEnvConfig.DEFAULT_FACEBOOK_PIXELID = `1164063764863535`
    GloablEnvConfig.DEFAULT_TIKTOK_PIXELID = ``
    GloablEnvConfig.DEFAULT_SNAPCHAT_PIXELID = `还没有，到时候问产品`
    GloablEnvConfig.DEFAULT_PINTEREST_PIXELID = ``
} else if (isBooktokModel) {
    GloablEnvConfig.DEFAULT_FACEBOOK_PIXELID = `690858557147997`
    GloablEnvConfig.DEFAULT_TIKTOK_PIXELID = ``
    GloablEnvConfig.DEFAULT_SNAPCHAT_PIXELID = `还没有，到时候问产品`
    GloablEnvConfig.DEFAULT_PINTEREST_PIXELID = ``
} else if (isNetficModel) {
    GloablEnvConfig.DEFAULT_FACEBOOK_PIXELID = `550893584758907`
    GloablEnvConfig.DEFAULT_TIKTOK_PIXELID = ``
    GloablEnvConfig.DEFAULT_SNAPCHAT_PIXELID = `还没有，到时候问产品`
    GloablEnvConfig.DEFAULT_PINTEREST_PIXELID = ``
}
else {
    alert(`${currentModleName}未配置 pixel id 配置`)
}


// 唤起app的 android schema
const andoridSchema = {
    magfic: 'magfic://',
    goodfic: 'fbgoodfic://',
    yestory: 'yestory://',
    novelaria: 'fbnovelaria://',
    booktok: 'booktok://', // 'fbbooktok://'
    netfic: 'netfic://',
}
if (isMagficModel) {
    GloablEnvConfig.ANDROID_SCHEMA = andoridSchema.magfic
} else if (isYestoryModel) {
    GloablEnvConfig.ANDROID_SCHEMA = andoridSchema.yestory
} else if (isGoodficModel) {
    GloablEnvConfig.ANDROID_SCHEMA = andoridSchema.goodfic
} else if (isNovelariaModel) {
    GloablEnvConfig.ANDROID_SCHEMA = andoridSchema.novelaria
} else if (isBooktokModel) {
    GloablEnvConfig.ANDROID_SCHEMA = andoridSchema.booktok
} else if (isNetficModel) {
    GloablEnvConfig.ANDROID_SCHEMA = andoridSchema.netfic
}
else {
    alert(`${currentModleName}未配置 ANDROID_SCHEMA`)
}

window.GloablEnvConfig = GloablEnvConfig
// console.log("🚀 ~ GloablEnvConfig:", GloablEnvConfig)
export default GloablEnvConfig