// custom-loader.js

module.exports = function (source) {
    /**
        const loaderUtils = require('loader-utils')  

        const options = loaderUtils.getOptions(this)    
        source = source.replace(/蒋梨花/g, options.name)    
        return `module.exports = ${JSON.stringify(sorce)}`    
        // 最终需要返回一段可执行的js脚本

        作者：梨花酱
        链接：https://juejin.cn/post/6861784748491669511
        来源：稀土掘金
        著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。
     */
    // 将源文件内容转换为大写
    const transformedContent = source.toUpperCase()

    // 返回转换后的内容
    return transformedContent
}
