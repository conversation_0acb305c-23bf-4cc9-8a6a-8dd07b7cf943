const { ConcatSource } = require("webpack-sources") // 用来写入
class CustomPlugin {  
    /**
         首先需要声明一个 class 构造函数
        在class里面定义一个apply方法，接收compiler作为参数表示这次打包的上下文。
        指定挂载的webpack事件钩子
        处理webpack内部实例的特定数据
        功能完成后调用webpack提供的回调

        作者：梨花酱
        链接：https://juejin.cn/post/6861784748491669511
        来源：稀土掘金
        著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。
     */
    constructor(options) { 
        // 获取传入的option信息    
        this.msg = options.msg  
    }  
    // 我们需要一个apply方法(为了获取compiler)，接收compiler作为参数表示这次打包的上下文。  
    apply (compiler) {    
        const msg = this. msg    // 指定挂载的 webpack 钩子函数    
        // 使用compiler钩子compilation，即编译（compilation）创建之后，执行插件。    
        compiler.hooks.compilation.tap("CustomPlugin", compilation => {      
        // compilation的 optimizeChunkAssets 钩子，可以利用这个钩子实现为每个文件插入信息      
            compilation.hooks.optimizeChunkAssets.tap("CustomPlugin", chunks => {        
                for (const chunk of chunks) {          
                    for (const file of chunk.files) {            
                        compilation.updateAsset(file, old => {                       
                            return new ConcatSource(msg,"\n", old);            
                        });          
                    }        
                }      
            })    
        })  
    }
}
module.exports = CustomPlugin