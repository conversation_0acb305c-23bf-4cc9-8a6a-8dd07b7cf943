(function (win) {
  var ua = win.navigator.userAgent.toLowerCase()
  var isAndroid = ua.indexOf('android') > -1
  var isIos = /iphone|ipad|ipod|ios/.test(ua)
  // 数据观察
  function Subject () {
    this.client = true
    // 所有订阅者
    this.subscribers = {}
    // 所有缓存数据
    this.cached = {}
  }
  Subject.prototype = {
    constructor: Subject,
    // 开始订阅
    subscribe: function (type, param, fn) {
      if (!fn) {
        fn = param
        param = ''
      }
      this.subscribers[type] = this.subscribers[type] || []
      this.subscribers[type].push(fn)
      // 通知客户端
      this.send(type, param)
    },
    // 发布数据
    next: function (type, data) {
      // 存储数据
      var cdata = {
        // 记录数据到达时间
        arriveTime: Date.now(),
        data: data
      }
      this.cached[type]
        ? this.cached[type].push(cdata)
        : (this.cached[type] = [cdata])
      // 向订阅者统一发布数据
      if (this.subscribers[type]) {
        var listeners = this.subscribers[type]
        listeners.forEach(function (_fn) {
          _fn(type, data)
        })
      }
      return this
    },
    // JS发送消息到客户端
    send: function (cmd, param, callBack) {
      var code = 'SUCCESS'
      if (isAndroid) {
        try {
          var returnValue = win.Meete.actionFromJs(cmd, stringifyParam(param))
          if (returnValue) {
            subject$.next(cmd, parseParam(cmd))
          }
        } catch (e) {
          subject$.client = false
          console.warn(`Not in Android webview: ${cmd} `, param)
          code = e
        }
      } else if (isIos) {
        try {
          // 通知IOS客户端需要什么数据
          win.webkit.messageHandlers.Meete.postMessage({
            cmd: cmd,
            param: param
          })
        } catch (e) {
          subject$.client = false
          console.warn(`Not in IOS webview: ${cmd}`, param)
          code = e
        }
      } else {
        code = new Error('Not in Android or IOS env.')
      }
      callBack && callBack(code)
    },
    getState: function () {
      try {
        if (win.Meete.actionFromJs || (win.webkit && win.webkit.messageHandlers.Meete.postMessage)
        ) {
          return true
        }
        return false
      } catch (error) {
        return false
      }
    },
    // 关闭加载框
    dismissLoading: function () {
      this.send('CLIENT_LOADING_HIDE')
    },
    // 关闭加载框
    showLoading: function () {
      this.send('CLIENT_LOADING_SHOW')
    },
    // 订阅获取用户accessToken
    subscribeClientLDeviceInfo: function (callBack) {
      this.subscribe('GET_DEVICE_INFO', function (cmd, param) {
        callBack(param)
      })
    },
    jumpPage: function (param) {
      this.send('JS_JUMP_PAGE', param)
    },
    back: function () {
      this.send('JS_GO_BACK')
    },
    share: function (param) {
      this.send('JS_SHARE_CLICKED', param)
    },
    // 统计事件
    statistics: function (param) {
      this.send('CLIENT_STATISTICS', param)
    }
  }

  var subject$ = new Subject()
  // 解析param
  function parseParam (param) {
    var data = ''
    try {
      data = JSON.parse(param)
    } catch (e) {
      data = param
    }
    return data
  }

  // 格式化param
  function stringifyParam (param) {
    var data = ''
    if (typeof param === 'undefined') data = ''
    else if (typeof param === 'string') data = param
    else data = JSON.stringify(param)
    return data
  }
  // 挂载在window下给客户端调用的函数
  win.actionToJs = function (cmd, param) {
    subject$.next(cmd, parseParam(param))
  }
  win.Meete = win.Meete || {}
  win.Meete.bridge = subject$
})(window)