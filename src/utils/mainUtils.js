const getUrlQuery = (url, ) => {
    let query = new URLSearchParams(window.location.search)
    return query
}
const debugerQueryEnum = {
    开启与原生的mock: 'openBridgeModel',
    关闭error页跳转: 'closeErrorLocation',
    专门测试sku: 'testPaySku',
    开启webview页面跳转: 'testPageLocation',
    解锁页mock数据: 'unlockMock',
    开启log调试板: 'openLog',

    当前sass项目: 'project',
    当前sass环境: 'env',
}
const debugerQueryCheck = (key,) => {
    let value = debugerQueryEnum[key]
    if (!value) {
        console.error(`debugerQueryCheck key:${key}}`)
    }
    return getUrlQuery().get(value)
}

// 睡眠 promise 函数
const sleep = (time) => {
    return new Promise((resolve, reject) => {
        setTimeout(() => {
            resolve()
        }, time)
    })
}
export default {
    debugerQueryCheck,
    sleep,
}
