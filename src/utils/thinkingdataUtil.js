import store from "@/store/index.js"
import thinkingdata from "@/utils/ta_js_sdk/thinkingdata.umd.min"
// const THINKDATA_APP_ID = window.GloablEnvConfig.VUE_APP_THINKDATA_APP_ID
const THINKDATA_SERVER_URL = window.GloablEnvConfig.VUE_APP_THINKDATA_SERVER_URL
const APP_THINKDATA_APP_ID_JSON = window.GloablEnvConfig.VUE_APP_THINKDATA_APP_ID_JSON

// 登录
const login = (account_id) => {
    thinkingdata.login(account_id)
}

// 设置静态公共属性
const setSuperProperties = (o) => {
    thinkingdata.setSuperProperties(o)
}

// 设置访客 ID
const identify = (id) => {
    thinkingdata.identify(id)
}

// 保存公共属性到vuex状态管理器中
const savePublicDevice = () => {
    let deviceId = thinkingdata.getDeviceId()
    let presetProperties = thinkingdata.getPresetProperties()
    let properties = presetProperties.toEventPresetProperties()

    store.dispatch("user/setShuShuDeviceMsg", {
        eventProperties: properties,
        distinctId: deviceId,
    })
}
let isInitThnkingData = false
// 数数 初始化
const init = (options) => {
    if (isInitThnkingData) {
        console.log(" Ta log 已经初始化过了")
    }
    console.log("🚀 ~ init ~ options:", options)
    let {
        // 媒体来源
        media_source,
        source,
        campaign,
        token,
    } = options

    
    // 数数id
    let thinkDataAppId
    // 需要的包
    let packageName = options.packageName || options.packgeName
    //
    if (!packageName || APP_THINKDATA_APP_ID_JSON === "") {
        console.log(" Ta log 没有传递查询的包")
        return
    }

    // 查询
    let thinkDataAppIdInfo = JSON.parse(APP_THINKDATA_APP_ID_JSON)
    let fined = thinkDataAppIdInfo.find((v) => {
        return v.packgeName === packageName
    })
    if (fined === undefined) {
        console.log("包查询失败")
        return
    }
    thinkDataAppId = fined.thinkDataAppId
    console.log("Ta log 包名查询成功：:", fined, thinkDataAppId)

    //  初始化数数
    var config = {
        appId: thinkDataAppId,
        serverUrl: THINKDATA_SERVER_URL,
    }
    thinkingdata.init(config)

    // 设置静态公共属性
    /**
         对于一些重要的属性，譬如用户的渠道、昵称、ID 等，这些属性需要设置在每个事件中，
        来设置静态公共事件属性，静态公共事件属性会对全局生效。
        当开启缓存时（默认打开），静态公共属性会缓存在 localStorage 或 cookie 中。
        静态公共属性的参数是一个 JSON 对象，其格式要求与事件属性保持一致。
     */
    setSuperProperties({
        media_source: media_source || source || '',
        campaign: campaign || '',
    })
    savePublicDevice()
    isInitThnkingData = true

    console.log('Ta log ======================= 调试 打点调试查询参数 ======================== Ta log ', )
    console.log('Ta', thinkingdata)
    console.log('_state', thinkingdata.persistence._state)
    console.log('device_id', thinkingdata.persistence._state.device_id)
    console.log('distinct_id', thinkingdata.persistence._state.distinct_id)
    console.log('Ta log ======================== 调试 打点调试查询参数 ======================== Ta log ', )
}

// 埋点事件名
const TRCK_EVENTS = {
    /**
     * 支付页面
     */
    // H5活动页面访问
    view: "activity_page_view",
    // H5活动页面完成加载
    finish: "activity_page_view_finish",
    // 活动页面付莞按转化钮点击
    payClick: "activity_page_click",

    /**
     * http 错误相关收集
     */
    // 页面打开失败  http状态码 && 返回后的code  判断
    pageFailed: "activity_page_failed",
}

window.o = []
// 发送 埋点
const sendTrck = (eventName, options) => {
    console.log("🚀  开始埋点 ~ ")
    console.log("🚀  sendTrck ~ eventName, options:", eventName, options)
    thinkingdata.track(eventName, options)
    console.log("🚀  结束埋点 ~ ")
    o = [
        ...o,
        {
            eventName,
            ...options,
        },
    ]
}

export default {
    init,
    login,
    TRCK_EVENTS,
    sendTrck,
    setSuperProperties,
    identify,
    savePublicDevice,
}
