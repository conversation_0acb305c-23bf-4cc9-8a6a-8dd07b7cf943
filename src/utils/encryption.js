import CryptoJS from "crypto-js";
import SHA512 from 'crypto-js/sha512';

const ACCESS_KEY= window.GloablEnvConfig.VUE_APP_ACCESS_KEY;
const AES_KEY= window.GloablEnvConfig.VUE_APP_AES_KEY;

//加密sign512
export  function EncryptSHA512(notice,time) {
  const params='notice='+notice+'&time='+time+'&accesskey='+ACCESS_KEY
  return SHA512(params).toString()

}


//加密AES
export  function EncryptAES(word) {

    let key = AES_KEY;
    let iv = AES_KEY;

    key = CryptoJS.enc.Utf8.parse(key);
    iv = CryptoJS.enc.Utf8.parse(iv);

    // 加密模式为CBC，补码方式为PKCS5Padding（也就是PKCS7）
    let encrypted = CryptoJS.AES.encrypt(word, key, {
            iv: iv,
            mode: CryptoJS.mode.CBC,
            padding: CryptoJS.pad.Pkcs7
    });

    //返回base64
    return CryptoJS.enc.Base64.stringify(encrypted.ciphertext);

}

//解密AES
export function DecryptAES(word) {
    let key = CryptoJS.enc.Utf8.parse(AES_KEY);
    let iv = CryptoJS.enc.Utf8.parse(AES_KEY);
    let base64 = CryptoJS.enc.Base64url.parse(word);
    let src = CryptoJS.enc.Base64.stringify(base64);
    let decrypt = CryptoJS.AES.decrypt(src, key, {
            iv: iv,
            mode: CryptoJS.mode.CBC,
            padding: CryptoJS.pad.NoPadding
    });

    let decryptedStr = decrypt.toString(CryptoJS.enc.Utf8);
    return decryptedStr.toString();

}