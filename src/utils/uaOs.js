var ua = window.navigator.userAgent.toLowerCase()
var isAndroid = ua.indexOf("android") > -1 || ua.indexOf("linux") > -1
var isIos = /iphone|ipad|ipod|ios/.test(ua)
function getUaOs() {
    var os = "WEB"
    try {
        if (isAndroid) {
            os = "ANDROID"
        } else if (isIos) {
            os = "IOS"
        }
    } catch (e) {
        console.warn(`Not in IOS webview: `, e)
        os = "WEB"
    }
    return os
}

/**
 * 
 * @returns 
 {
    "os": {
        "name": "Android",
        "version": 8
    },
    "browser": {
        "name": "Chrome",
        "version": 116
    }
 }
 */
// 抄来的代码 
// https://medium.com/creative-technology-concepts-code/detect-device-browser-and-version-using-javascript-8b511906745
// https://www.jianshu.com/p/fbd48abcd8d7
const getNavigatorAbout = () => {
    let webLog = {}
    let userAgent = navigator.userAgent
    // 获取微信版本
    let m1 = userAgent.match(/MicroMessenger.*?(?= )/)
    if (m1 && m1.length > 0) {
        webLog.wechat = m1[0]
    }
    // 苹果手机
    if (userAgent.includes("iPhone") || userAgent.includes("iPad")) {
        // 获取设备名
        if (userAgent.includes("iPad")) {
            webLog.device = "iPad"
        } else {
            webLog.device = "iPhone"
        }
        // 获取操作系统版本
        m1 = userAgent.match(/iPhone OS .*?(?= )/)
        if (m1 && m1.length > 0) {
            webLog.system = m1[0]
        }
    }
    // 安卓手机
    if (userAgent.includes("Android")) {
        // 获取设备名
        m1 = userAgent.match(/Android.*; ?(.*(?= Build))/)
        if (m1 && m1.length > 1) {
            webLog.device = m1[1]
        }
        // 获取操作系统版本
        m1 = userAgent.match(/Android.*?(?=;)/)
        if (m1 && m1.length > 0) {
            webLog.system = m1[0]
        }
    }

    let e = webLog
    let system = e.system
    let version = ''
    // if (Array.isArray(system)) {
    //     version = system.split(" ").at(-1)
    // }
    if (system && system.split(' ').length > 1) {
        version = system.split(" ").at(-1)
        // android 是 8.0.0.  ios 是 16_0_0 格式 
        // 统一转换为 *.*.*
        if (version.includes('_')) {
            version = version.replace(/_/g, '.')
        }
    }
    e.version = version
    console.log("🚀 ~ getNavigatorAbout ~ e:", e)
    return e
}
// getNavigatorAbout()

module.exports = {
    getUaOs,
    getNavigatorAbout,
}
