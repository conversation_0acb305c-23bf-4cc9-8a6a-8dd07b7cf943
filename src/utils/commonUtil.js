import store from '@/store'
import httpService from "@/apis/request";
import thinkingdata from '@/utils/ta_js_sdk/thinkingdata.umd.min';
import uaOs from '@/utils/uaOs.js';
import moment from 'moment-timezone';

export function anonymousLogin(){
    var deviceId = thinkingdata.getDeviceId();
    var presetProperties = thinkingdata.getPresetProperties();
    var properties = presetProperties.toEventPresetProperties();
    var packageName= window.GloablEnvConfig.VUE_APP_PACKAGENAME;

    var rotePackageName=to.query.packageName;
    var os='WEB';
    if(undefined !=rotePackageName && null !=rotePackageName && '' !=rotePackageName){
        os=uaOs.getUaOs();
        console.log("os:",os);
        packageName=rotePackageName;
    }

    const eventDto = {
        eventProperties: properties,
        distinctId: deviceId,
        eventType: 'create_account',
    };
    store.dispatch('user/setShuShuDeviceMsg', {eventProperties: properties,
        distinctId: deviceId,});
    const timeZone = moment.tz.guess()
    httpService({
        url: "api/web/ebook/user/anonymous_login",
        method: "post",
    }, {
        clientUuid: window.navigator.userAgent,
        packageName: packageName,
        os: os,
        timeZone: timeZone,
        fallbackUrl: '',
        fallbackUrl2: null,
        eventDto: eventDto
    }).then((data) => {
        thinkingdata.setSuperProperties({ user_id: data.uuid ? data.uuid :''});
        store.dispatch('user/setToken', data.token);
        store.dispatch('user/setUserInfo', data);
    }).catch((err) => {
        console.error("anonymous_login error:",err);
    });
}

