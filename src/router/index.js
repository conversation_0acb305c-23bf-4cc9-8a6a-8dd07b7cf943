/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-02-02 15:21:22
 * @LastEditors: chen<PERSON><PERSON>
 * @Description:
 * @LastEditTime: 2023-05-29 19:23:25
 */
import Vue from 'vue'
import VueRouter from 'vue-router'
import Home from '../views/Home.vue'
import Popularize from '../views/popularize.vue'
import Deepchain from '../views/deepchain.vue'
import test from '../views/test.vue'
import error from '../views/error.vue'
import * as nameConfig from '~/sassConfig/nameConfig.js'


Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: {
      title: 'Read Next Chapter Now 👉',
      // requiresAuth: true
    }
  },{
    path: '/test',
    name: 'test',
    component: test,
    meta: {
      title: 'test',
      requiresAuth: true
    }
  },
  {
    path: '/popularize',
    name: 'popularize',
    component: Popularize,
    meta: {
      title: 'Read Next Chapter Now 👉',
      requiresAuth: true
    }
  },
  {
    path: '/deepchain',
    name: 'deepchain',
    component: Deepchain,
    meta: {
      title: 'Deep Chain 👉',
      requiresAuth: true
    }
  },
  {
    path: '/error',
    name: 'error',
    component: error,
    meta: {
      title: 'error'
    }
  },
]


// let publicPath = '/' //默认路径
// if(process.env.PROJECT_SERVER_PATH){
//   publicPath = `/${process.env.PROJECT_SERVER_PATH}/`
// }
const router = new VueRouter({
  base: nameConfig.currentUploadSeverPath,
  mode: 'history',
  routes,
  scrollBehavior() {
    return { x: 0, y: 0 }
  }
})

router.beforeEach((to, from, next) => {
  next();
});



export default router
