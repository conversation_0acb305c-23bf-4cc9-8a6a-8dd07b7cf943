/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-11-10 15:30:24
 * @LastEditors: chen<PERSON><PERSON>
 * @Description:
 * @LastEditTime: 2023-02-28 16:52:59
 */
import router from './index'
import setCookie from "@/utils/setCookie.js";
import thinkingdataUtil from "@/utils/thinkingdataUtil"
router.beforeEach((to, from, next ) => {
    window.document.title = to.meta.title
    // TODO 根据路由配置过滤权限
    if (to.query.testSecretKey){
        setCookie('testSecretKey',to.query.testSecretKey)
    }
    thinkingdataUtil.init(to.query)
    next()
})
