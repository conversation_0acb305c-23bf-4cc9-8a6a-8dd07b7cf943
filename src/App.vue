console.log('m1===', m1, 'm2'<!--
 * @Author: ch<PERSON>wei<PERSON>
 * @Date: 2023-02-02 15:21:22
 * @LastEditors: chenwei<PERSON>
 * @Description:
 * @LastEditTime: 2023-05-29 19:27:49
-->
<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
import disableDevtool from 'disable-devtool';
export default {
  created(){
    const options={
      url:window.location.origin+'/error',
      disableSelect: true, // 是否禁用选择文本 默认为false
      debugDelay: 300, // debug模式时的延迟 默认200ms
      interval: 300, // 定时器的时间间隔 默认200ms
      disableMenu: true, // 是否禁用右键菜单 默认为true
      md5: 'cf04d64b0224b0dd7a5e567af44830f1'
    }

    let checkAntiCheat = window.GloablEnvConfig.isBuildScript || window.GloablEnvConfig.isDevScript
    if (checkAntiCheat) {
      let m1 = this.$route.query.stop == "true"
      let m2 = localStorage.getItem('DebugUtilOpen')
      if (m1 && !m2) {
        localStorage.setItem('DebugUtilOpen', 1)
      }
      // console.log('m1===', m1, 'm2', m2)
      // alert(m1)
      // alert(m2)
      if (m1 || m2) {
        // debug 调试模式，不禁止f12
        // disableDevtool(options);
        // disableDevtool.isSuspend = true;
      } else {
        
        disableDevtool(options);
      }
    }
  }
};
</script>

<style lang="scss">
</style>
