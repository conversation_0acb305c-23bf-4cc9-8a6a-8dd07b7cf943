/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-02-02 15:21:22
 * @LastEditors: chenwei<PERSON>
 * @Description:
 * @LastEditTime: 2023-02-27 15:28:52
 */
// require('~/sassConfig/GloablEnvConfig.js')
import envs from '~/sassConfig/GloablEnvConfig.js'
const favicon = document.querySelector('[rel="icon"]')
// favicon.href = './magfic__favicon.ico' 
// favicon.href = './goodfic__favicon.ico' 
// favicon.href = `./yestory__favicon.ico` 
// favicon.href = `./${GloablEnvConfig.currentModleName}__favicon.ico` 
// let e = require('./img/img_magfic/magfic__favicon.ico')
// favicon.href = e

let name = GloablEnvConfig.currentModleName
let path = `img_${name}/${name}__favicon.ico`
favicon.href = require(`./img/${path}`)

import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import '@/utils/bridge'

Vue.config.productionTip = false
import { Loading, Toast, Button, Dialog } from 'vant';
import 'vant/lib/toast/style'
import 'vant/lib/loading/style'
import 'vant/lib/button/style'
import 'vant/lib/dialog/style'
import '@/router/router_promission'
import  httpService  from "./apis/request";
import VueClipboard from 'vue-clipboard2'
Vue.prototype.$httpService = httpService;
import mainUtils from '@/utils/mainUtils.js'

import thinkingdata from './utils/ta_js_sdk/thinkingdata.umd.min'
Vue.prototype.$ta = thinkingdata;
window.$ta = thinkingdata;
import VConsole from 'vconsole';


import {
  isProductionEnv,
} from '~/sassConfig/nameConfig.js'
const showLog = mainUtils.debugerQueryCheck('开启log调试板') || !isProductionEnv
if (showLog) {
  new VConsole()
  // 打印构建时间，便于确认版本
  if (process.env.BUILD_TIMESTAMP) {
    // console.log(`🏗️ 构建时间戳: ${process.env.BUILD_TIMESTAMP}`)
    // console.log(`🏗️ 构建时间: ${new Date(parseInt(process.env.BUILD_TIMESTAMP)).toLocaleString()}`)
  }
}
 
VueClipboard.config.autoSetContainer = true // add this line
Vue.use(VueClipboard)

Vue.use(Toast);
Vue.use(Loading);
Vue.use(Button);
Vue.use(Dialog);
new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')

