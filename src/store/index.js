/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-11-01 16:55:13
 * @LastEditors: chenwei<PERSON>
 * @Description: 
 * @LastEditTime: 2023-02-28 14:34:06
 */
import Vue from 'vue'
import Vuex from 'vuex'
import createPersistedstate from 'vuex-persistedstate'
 
Vue.use(Vuex)
import user from './modules/user'


export default new Vuex.Store({
  modules: {
    user
  },
   // 将插件配置到Vuex的plugins中
   plugins: [
    createPersistedstate({
      storage: window.localStorage,
      key: 'magfic_web', // 存数据的key名   自定义的  要有语义化
      paths: ['user'], // 要把那些模块加入缓存
      render(state) {
        // 要存储的数据：本项目采用es6扩展运算符的方式存储了state中所有的数据
        return { ...state };
      }
    })
  ]
})
