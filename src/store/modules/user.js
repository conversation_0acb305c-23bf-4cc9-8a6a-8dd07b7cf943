/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2022-11-09 10:49:56
 * @LastEditors: chen<PERSON><PERSON>
 * @Description: 用户信息
 * @LastEditTime: 2023-02-06 15:03:54
 */
export default {
  namespaced: true,
  state: () => ({
    userLang: "en", // 用户语言
    token: "", //登陆token
    userInfo: {},
    shushuDeviceMsg:{},//数数设备信息
    acceptCookies: false,
    media_source:'',
    campaign:'',
    searchHistory:[],
    afr:{}
  }),
  mutations: {
    SET_TOKEN(state, token) {
      state.token = token;
    },
    SET_USERINFO(state, userInfo) {
      state.userInfo = userInfo;
    },
    SET_ACCEPTCOOKIES(state, acceptCookies) {
      state.acceptCookies = acceptCookies;
    },
    SET_MEDIASOURCE(state, media_source) {
      state.media_source = media_source;
    },
    SET_CAMPAIGN(state, campaign) {
      state.campaign = campaign;
    },
    SET_SHUSHUDEVICEMSG(state, shushuDeviceMsg) {
      state.shushuDeviceMsg = shushuDeviceMsg;
    },
    SET_SEARCHHISTOTY(state, searchHistory) {
      state.searchHistory = searchHistory;
    },
    SET_AFR(state, afr) {
      state.afr = afr;
    }
  },
  actions: {
    setToken(context, token) {
      context.commit("SET_TOKEN", token);
    },
    setUserInfo(context, userInfo) {
      context.commit("SET_USERINFO", userInfo);
    },
    setAcceptCookies(context, acceptCookies) {
      context.commit("SET_ACCEPTCOOKIES", acceptCookies);
    },
    setMediaSource(context, media_source) {
      context.commit("SET_MEDIASOURCE", media_source);
    },
    setCampaign(context, campaign) {
      context.commit("SET_CAMPAIGN", campaign);
    },
    setShuShuDeviceMsg(context, shushuDeviceMsg) {
      context.commit("SET_SHUSHUDEVICEMSG", shushuDeviceMsg);
    },
    setSearchHistory(context, searchHistory) {
      context.commit("SET_SEARCHHISTOTY", searchHistory);
    },
    setAfr(context, afr) {
      context.commit("SET_AFR", afr);
    }
  }
};
