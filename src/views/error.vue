<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-03-08 17:49:56
 * @LastEditors: chenwei<PERSON>
 * @Description: 
 * @LastEditTime: 2023-03-08 17:57:20
-->
<!--  -->
<template>
<div class='error-page'>
    <img src="@/img/img_public/common/error.png" alt="" srcset="">
    <p class="content">Something get wrong</p>
</div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
</script>
<style lang='scss' scoped>
//@import url(); 引入公共css类
.error-page{
    height: 100vh;
    width: 100vw;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}
.content{
    margin-top: 33px;
    font-size: 29px;
    font-family: Inter-Regular, Inter;
    font-weight: 400;
    color: #2E2E2E;
    line-height: 42px;
}
</style>