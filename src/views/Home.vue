<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-02-02 15:21:22
 * @LastEditors: chen<PERSON><PERSON>
 * @Description:
 * @LastEditTime: 2023-03-09 15:54:33
-->
<template>
  <div class="pages">
    <!-- <titleTbr title="Book Name"></titleTbr> -->
    <div class="page-content">
      <div class="cover">
        <img :src="bookData.cover" alt="" srcset="" />
      </div>
      <div class="book-title">{{ bookData.bookName }}</div>
      <div class="content-canvas" id="content-canvas">

      <div v-for="(chapter,index) in chapterContentArray" :key="index">
        <div class="book-content" >{{ chapter }}</div>
      </div>
<!--        <div class="book-content" id="book-content">
         {{ bookData.shareChaperDetail.chapterContent }}
        </div>-->
      </div>
      <div class="download-btn" @click="go">Continue Reading</div>
    </div>
  </div>
</template>

<script>
import titleTbr from "@/components/titleTbr";
import html2canvas from "html2canvas";
import { mapState } from "vuex";
import thinkingdataUtil from "@/utils/thinkingdataUtil"
import {
    isMagficModel,
    isYestoryModel,
    isGoodficModel,
    isNovelariaModel,
} from '../../sassConfig/nameConfig'

export default {
  name: "Home",
  components: { titleTbr },
  data() {
    return {
      bookData: {
        shareChaperDetail: {
          chapterContent: "",
        },
      },
      chapterContentArray:[],
    };
  },
  created() {
    this.getBookDetail();
  },
  computed: {
    ...mapState("user", ["shushuDeviceMsg"]),
  },
  mounted() {
  },
  methods: {
    // 打点
    handlePoint(eventName) {
      console.log("🚀 ~ handlePoint ~ this.bookData:", this.bookData)
      let shareKey = 'share_channel'
      let share_channel1 = this.$route.query[shareKey]
      let share_channel2 = this.$route.query[`amp;${shareKey}`]
      console.log('调试=== share_channel query', this.$route.query)
      console.log('调试=== share_channel1', share_channel1,)
      console.log('调试=== share_channel2', share_channel2,)
      let from
      let resultFrom = {
        '邮箱分享': 'email_link',
        '正常分享': 'share_link',
      }
      /**
       *  因为使用 gomo 生成的链接会再解析一次
       * 比如                   www.baidu.com/?a=1&b=2
       * 运营配置gomo的时候会变成   www.baidu.com/?a=1&amp;b=2
       */
      if (share_channel1 === 'email' || share_channel2 === 'email') {
        from = resultFrom.邮箱分享
      } else {
        from = resultFrom.正常分享
      }
      
      this.$ta.track(
          eventName,
          {
            book_id: this.bookData.bookId,
            from_account_id: this.bookData.shareUuid,
            User_agent: window.navigator.userAgent,
            share_link: window.location.href,
            from: from,
          }
        );
    },
    setUuConfig(packageName) {
      // 1。初始化数数
      thinkingdataUtil.init({
        // packageName: "com.magfic.novel.buu" // android
        // packageName: "com.magfic.buu", // ios
        packageName: packageName,
      })
      thinkingdataUtil.savePublicDevice()
      // 
      this.handlePoint('sharepage_view')
    },
    go(){
      let url
      if (isMagficModel) {
        url = `https://magfic.onelink.me/oxJU?af_xp=social&pid=share&c=share&af_dp=magfic://&deep_link_value=reading_page&deep_link_sub1=${this.bookData.bookId}`;
      } else if (isYestoryModel) {
        url = `https://yestory.onelink.me/Betn?af_xp=social&pid=share&c=share&deep_link_sub1=${this.bookData.bookId}&af_dp=yestory://&is_retargeting=true&deep_link_value=reading_page`;
      } else if (isGoodficModel) {
      } else if (isNovelariaModel) {
      } 
      if (this.bookData.bookName) {
        this.handlePoint('download_click')
      } else {
        console.log('数据没加载完成，不埋点')
      }
      console.log('调试===', url)

      if (!url) {
        console.error('未定义')
        return
      } else {
        window.location.href=url
      }
    },
    getBookDetail() {
      const url =
        "api/web/ebook/book/share_book" +
        "?callData=" +
        this.$route.query.callData;

      const eventDto = {
        ...this.shushuDeviceMsg,
        eventType: "sharepage_open",
      };

      this.$httpService(
        {
          url,
          method: "post",
        },
        {
          shareLink:window.location.href,
          eventDto
        }

      ).then((data) => {
        console.log("🚀 ~ ).then ~ data:", data)
        if(undefined != data.shareChaperDetail && undefined != data.shareChaperDetail.chapterContent){
          this.chapterContentArray=data.shareChaperDetail.chapterContent.split("<br>").filter(item => item.trim() !== "");
        }
        this.bookData = data;
        document.title=data.bookName;

        var metaTitle = document.createElement("meta");
        metaTitle.name = "og:title";
        metaTitle.property = "og:title";
        metaTitle.content = data.bookName;
        document.head.appendChild(metaTitle);

        var metaDescription = document.createElement("meta");
        metaDescription.name = "og:description";
        metaDescription.property = "og:description";
        metaDescription.content = data.description;
        document.head.appendChild(metaDescription);

        var metaImg = document.createElement("meta");
        metaImg.name = "og:image";
        metaImg.property = "og:image";
        metaImg.content = data.cover;
        document.head.appendChild(metaImg);

        this.setUuConfig(data.packageName)
      });
    },
  },
};


</script>


<style lang="scss" scoped>
.content-canvas{
  padding-bottom: 180px;
}
.pages {
  min-height: 100vh;
  background: #f8f8fa;
  .page-content {
    padding: 0 42px;
    padding-top: 113px;
    .cover {
      text-align: center;
      margin-bottom: 50px;
      img {
        width: 354px;
        height: 496px;
        border-radius: 29px;
      }
    }
    .book-title {
      height: 67px;
      text-align: center;
      font-size: 42px;
      font-family: Poppins-SemiBold, Poppins;
      font-weight: 600;
      color: #252b37;
      line-height: 67px;
      margin-bottom: 38px;
    }
    .book-content {
      font-size: 33px;
      font-family: Poppins-Regular, Poppins;
      font-weight: 400;
      color: #252b37;
      line-height: 54px;
    }
    .download-btn {
      position: fixed;
      bottom: 44px;
      width: 667px;
      height: 113px;
      background: linear-gradient(315deg, #ff5385 0%, #ff67ca 100%);
      border-radius: 56px;
      font-size: 33px;
      font-family: Poppins-SemiBold, Poppins;
      font-weight: 600;
      color: #ffffff;
      line-height: 113px;
      text-align: center;
    }
  }
}
</style>
