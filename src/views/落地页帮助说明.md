### Facebook & TikTok 归因参数映射表 「归因public() 函数方法总结的」

| OneLink 参数 | Facebook Key | TikTok Key | 说明 |
| :--- | :--- | :--- | :--- |
| `mediaSource` | `source` | `source` | 媒体源 |
| `campaign` | `utm_campaign` | `af_c` | 广告系列名称 |
| `afSub1` | `fbclid` | `ttclid` | 广告点击ID |
| `deepLinkValue` | `jump_path` | `jump_path` | 深度链接跳转路径 |
| `deep_link_sub1` | `book_id` | `book_id` | 书本ID |
| `ad` | `utm_ad` | `af_ad` | 广告名称 |
| `adSet` | `utm_adset` | `af_adset` | 广告组名称 |
| `af_campaignid` | `utm_campaignid` | `af_c_id` | 广告系列ID |
| `deep_link_sub2` | `chapter_id` | `chapter_id` | 章节ID |
| `deep_link_sub3` | `source` | `source` | 媒体源 (再次映射) |
| `deep_link_sub4` | `utm_campaign` | `af_c` | 广告系列名称 (再次映射) |
| `deep_link_sub5` | `utm_campaignid` | `af_c_id` | 广告系列ID (再次映射) |
| `deep_link_sub6` | `utm_ad` | `af_ad` | 广告名称 (再次映射) |
| `deep_link_sub7` | `utm_adset` | `af_adset` | 广告组名称 (再次映射) |
| `deep_link_sub8` | `book_version` | `book_version` | 书本版本 |
| `deep_link_sub9` | `fbclid` | `ttclid` | 广告点击ID (再次映射) |
| `af_r` | `af_r` | `af_r` | 跳转URL |

---

**补充说明:**

*   `googleClickIdKey` 被硬编码为 `af_sub2`，用于传递 Google 的点击ID (`gclid`)，不在此方法中处理。
*   多个 `deep_link_sub` 参数 (`sub3` 到 `sub9`) 重复映射了主要的归因参数，这可能是为了确保数据在不同维度的报表中都能被正确统计。
*   当 Facebook 和 TikTok 的参数键名相同时，表示这两个渠道使用同一个 URL 参数名来传递该信息。当它们不同时，此代码逻辑会根据渠道来源正确地从 URL 中提取对应的参数值。

---

### 广告渠道 Cookie 获取逻辑

**目标**: 为了精确归因，需要在上报数据 (`clickIpAttribution` 方法) 前，确保获取到由 Facebook (`_fbp`, `_fbc`) 或 TikTok (`_ttp`) 广告脚本写入浏览器的 Cookie。

**问题**: 广告脚本是异步加载的，在我们的代码运行时，可能还未执行完毕，导致无法立即获取到 Cookie。

**解决方案**:
1.  **立即尝试**: 首先直接尝试获取 Cookie。
2.  **轮询备用**: 如果获取失败，则启动一个每 100ms 重试一次的定时器，直到成功获取到 Cookie 为止。
3.  **上报数据**: 一旦获取到 Cookie，就调用 `clickIpAttribution()` 方法，将包含完整 Cookie 的归因数据上报给服务器。

这个“立即尝试 + 轮询备用”的机制，解决了代码执行与第三方脚本加载的时序问题，保证了归因数据的准确性。