import { getUrlParams } from './ChannelChange'

export const px2vw = (px) => {
    let str = px
    if (!str) {
        return str
    }
    if (str.length) {
        let s1 = str.at(-2)
        let s2 = str.at(-1)
        if (s1 == 'p' && s2 === 'x') {
            str = str.slice(0, -2)
        }
    }
    return `${(100 / 750) * str}vw`
}
const isObject = (e) => Object.prototype.toString.call(e) === "[object Object]"
const lineStyleFilter = (styleMap) => {
    let sm = structuredClone(styleMap)
     const remFilter = [
         'fontSize',
     ]
     // 传输的格式如 {fontSize: '24px',fontWeight: '400',} 这里的 fontSize 就需要特殊处理了
     if (isObject(sm) && Object.keys(sm).length) {
         for (const key of Object.keys(sm)) {
             if (remFilter.includes(key)) {
                 sm[key] = px2vw(sm[key])
             }
         }
     }
     return sm
 }
 let m = {fontSize: '24px',fontWeight: '400',}
 let s = lineStyleFilter(m)
//  console.log("🚀 ~ s:", s)

const PopularizeFetchEnum = {
    // 小说详情
    switchEnum: {
        开启: 1,
        关闭: 0,
    },
    // 置顶下载按钮类型
    innterhtmlTypeNum: {
        图片: 'PICTURE',
        文字: 'HTML',
    },
    // 小说详情
    themeTypeEnum: {
        主题1: 1,
        主题2: 2,
        主题3: 3,
    },
    // 引言样式
    introStyleEnum: {
        引言类型1: 1,
        引言类型2: 2,
        引言类型3: 3,
    },
    // 小说详情
    novelDetail: 1,
    // 转换按钮类型
    transButtonTypeEnum: {
        默认0: 0,
        手指1: 1,
        缩放2: 2,
        发光3: 3,
        流动4: 4,
    },
}
let getPixelId = () => {
    let defaultPixelId = '9999999'
    let pixelId = null
    let channel = getUrlParams('channel') && getUrlParams('channel').toLowerCase()
    console.log("🚀 ~ getPixelId ~ channel:", channel)
    let id = null
    /**
     * 此代码需要优化
     */
    if (channel === 'snapchat_ip_int') {
        id = GloablEnvConfig.DEFAULT_SNAPCHAT_PIXELID
    } else if (channel === 'pinterest_ip_int') {
        id = GloablEnvConfig.DEFAULT_PINTEREST_PIXELID
    } else if (channel === 'facebook_ip_int') {
        id = GloablEnvConfig.DEFAULT_FACEBOOK_PIXELID
    } else if (channel === 'tiktok_ip_int') {
        id = GloablEnvConfig.DEFAULT_TIKTOK_PIXELID
    }
    if (id) {
        pixelId = id
    } else {
        pixelId = defaultPixelId
        console.error('MockData数据: pixelId 未设置，是否正确')
    }
    return pixelId
}

const e = {
    // _debugShowAllType: true, // 这个参数只有我模拟的时候有
    _debugShowAllType: false, // 这个参数只有我模拟的时候有
    authorName: "Icon_Brat101",
    bookId: 1655,
    chapterNumber: 6,
    comment: "The Princess Strikes Back!",
    cover: "https://ax89.cdn.imagelib.net/book/html_popularize_test/9cd6e629413f589a38665ae77e6cb2b.png",
    // cover: "https://www.30secondsofcode.org/assets/splash/camera.webp",
    bannerUrl: require('../img/img_public/ezgif.com-gif-maker.webp'),
    description:
        '"Catalaya has been abused and treated like a slave by her family and pack members alike for as long as she can remember. Why they do this she has no idea. The only people who stood up for her were her five best friends and her brother.\r\n\r\nAfter getting humiliated and rejected by her mate and finding out the people who raised her weren\'t her parents in front of the whole pack she decides enough is enough and runs away. Little did she know that her life had just begun and she would be in for one hell of a ride."',
    followNumber: 6863,
    jumpUrls: ["https://apps.apple.com/us/app/id1621027852"],
    popularizeChapterDetails: [
        {
            bookId: 1655,
            chapterContent: [
                "#Chapter1",
                "PROLOGUE",
                "Lucifer sat in his throne room reminiscing as he looked at the last picture he took of the only woman he ever loved, his Olivia.",
            ],
            chapterId: 2068350,
            chapterName: "Chapter1\r",
            chapterNo: 1,
            words: 610,
        },
    ],
    readNumber: 8518,
    recommend:
        "💥Catalaya & Noah💥: You are the world's most ugliest freak and there is no way you could be my mate so I Noah reject you Catalaya as my mate.",
    style: 1,
    tagInfos: [
        {
            id: 82,
            isHot: 1,
            tagName: "Werewolf",
        },
    ],
    targetAgeMsg: "16+",
    version: "0",
    versionName: "origin",

    bookName: " Capturing My Victim ",

    /**
     * 1 默认主题
     * 2 主题2
     * 3 主题3
     */
    themeType: PopularizeFetchEnum.themeTypeEnum.主题1,
    themeType: PopularizeFetchEnum.themeTypeEnum.主题2,
    themeType: PopularizeFetchEnum.themeTypeEnum.主题3,
    // 4.0 样式需求
    topDownloadAbout: {
        isShowTopDownload: PopularizeFetchEnum.switchEnum.开启, // 下载按钮区域是否展示开关 1 展示 0 不展示
        // isShowTopDownload: PopularizeFetchEnum.switchEnum.关闭, // 下载按钮区域是否展示开关 1 展示 0 不展示

        topDownloadCssType: PopularizeFetchEnum.innterhtmlTypeNum.文字, // PICTURE |  html
        /**
         * topDownloadCssType: PopularizeFetchEnum.innterhtmlTypeNum.图片, // PICTURE |  html
        topDownloadPicUrl: require('../img/img_public/test.jpg'),
         */
        // 整个的样式
        topDownloadBackgroundCss: {
            backgroundColor: '#000',
        },
        topDownloadBackgroundCss: {},

        // 标题文案
        topDownloadTitle: 'The Alpha Prince The Alpha Prince The Alpha Prince',
        // 标题样式
        topDownloadTitleCss: {
            // 标题字体颜色
            color: 'black',
            // 标题字体大小
            fontSize: '38px',
            // 标题字体粗细
            fontWeight: '400',
        },
        topDownloadTitleCss: {},

        // 内容文案
        topDownloadContent: 'It was early May. In some of',
        // 内容样式
        topDownloadContentCss: {
            // 内容字体颜色
            color: 'black',
            // 内容字体大小
            fontSize: '28px',
            // 内容字体粗细
            fontWeight: '400',
        },
        topDownloadContentCss: {},


        // 下载文案
        topDownloadButtonText: '下载哦',
        // 内容样式
        topDownloadButtonCss: {
            // 内容字体颜色
            color: 'blue',
            // 内容字体大小
            fontSize: '28px',
            // 内容字体粗细
            fontWeight: '400',
            background: 'linear-gradient(180deg, #e8e8fe, #fe5450)'
        },
        topDownloadButtonCss: {},
    },
    // 内容引言 1
    introAbout: {
        isShowIntro: PopularizeFetchEnum.switchEnum.开启, // 内容引言是否展示开关 1 展示 0 不展示
        // isShowIntro: PopularizeFetchEnum.switchEnum.关闭, // 内容引言是否展示开关 1 展示 0 不展示

        introStyle: PopularizeFetchEnum.introStyleEnum.引言类型1, // 三种样式 1-2-3
        // introStyle: PopularizeFetchEnum.introStyleEnum.引言类型2, // 三种样式 1-2-3
        // introStyle: PopularizeFetchEnum.introStyleEnum.引言类型3, // 三种样式 1-2-3
        introText: '🤔It was early May. In some of the gardens the fruit trees were encircled with dense4 clusters of daffodils. The birches were already in pale with dense4 clusters ',
        // introText: '\n\n\t\n\n\n\n\n\n\t💗It was early May. In some  of of of ofoffd',
        introTextCss: {
            // 内容字体颜色
            color: 'green',
            color: 'black',
            // 内容字体大小
            // fontSize: '30px',
            // fontSize: '60px',
            // 内容字体粗细
            fontWeight: '400',
        }
    },
    // 自定义样式需求
    firstIeditText: `
    <div style="background: #FFEDCA;padding: 10px;">富文本区域1
        <span style="color: rgb(255, 169, 64); background-color: rgb(207, 19, 34); font-size: 14px;" >
            <s><strong>------</strong></s>
        </span>
        <p>
            <span style="color: rgb(255, 169, 64); font-size: 15px;"><strong>哎~。。。</strong></span>
        </p>
        <p>
            <span style="color: rgb(38, 38, 38); font-size: 15px;"><u><strong>咩呀</strong></u></span>
        </p>
        <p>
            <span style="color: rgb(255, 169, 64); background-color: rgb(207, 19, 34); font-size: 24px;"><s><strong>------</strong></s>
        </p>
    </div>
    `,
    firstIeditText: `
    123337363731233373637312333736373123337363731233373637312333736373
    `,

    secondIeditText: `
    <div style="background: #FFEDCA;padding: 10px;">富文本区域2
        <span style="color: rgb(255, 169, 64); background-color: rgb(207, 19, 34); font-size: 24px;" >
            <s><strong>------</strong></s>
        </span>

        <p>
            <span style="color: rgb(38, 38, 38); font-size: 15px;">
                123337363731233373637312333736373123337363731233373637312333736373
            </span>
        </p>
    </div>
    `,
    // 主背景
    backgroundCss: {
        backgroundColor: '#FBF8F2',
        fontSize: '42px',
    },
    // 小说内容
    chapterContentCss: {
        color: 'black',
        fontSize: '38px',
        fontWeight: '400',
    },
    // 滚动到底部弹窗
    transGuideCssType: PopularizeFetchEnum.innterhtmlTypeNum.图片, // PICTURE | HTML
    // transGuideCssType: PopularizeFetchEnum.innterhtmlTypeNum.文字, // PICTURE | HTML
    transGuideText: `
        🔥Click here to read more exciting content <br/ >
        👇🏻👇🏻👇🏻
    `,
    transGuideTextCss: {
        color: 'black',
        fontSize: '32px',
        fontWeight: '400',
    },
    transGuidePicUrl: require('../img/img_public/按钮引导动图.webp'),
    // 转换按钮     点击到商城
    transButtonCssType: 1 == 1 ? 'HTML' : 'PICTURE',
    transButtonPicUrl: require('../img/img_public/按钮@2x (1).png'),
    buttonText: "你好呀",
    transButtonCss: {
        color: 'red',
        fontSize: '33px',
        fontWeight: '800',
        background: 'linear-gradient(180deg, #e8e8fe, #fe5450)',

        color: '#fff',
        // background: '#fe1e53',
        background: 'linear-gradient(180deg, #FF0E56, #FF386A)',
        _emphasizeBoxShadowColor: '#fe1e53', // 阴影todo【需要后端增加】
    },
    // 底部提示样式
    transButtonTipText: `
        Read the fullnRead the
    ` 
    + `增加高度，yahaha ~`
    ,
    // transButtonTipCss
    transButtonTipCss: {
        color: 'black',
        fontSize: '24px',
        fontWeight: '400',
    },
    transButtonAnimationParam: {
        buttonAnimationType: PopularizeFetchEnum.transButtonTypeEnum.默认0,
        buttonAnimationType: PopularizeFetchEnum.transButtonTypeEnum.手指1,
        buttonAnimationType: PopularizeFetchEnum.transButtonTypeEnum.缩放2,
        buttonAnimationType: PopularizeFetchEnum.transButtonTypeEnum.流动4,
        
        buttonAnimationType: PopularizeFetchEnum.transButtonTypeEnum.发光3,
        buttonAnimationType: PopularizeFetchEnum.transButtonTypeEnum.手指1,
        glowingShadowColor: '#fe1e53'
    },
    // transButtonType: PopularizeFetchEnum.transButtonTypeEnum.默认0,
    // transButtonType: PopularizeFetchEnum.transButtonTypeEnum.手指1,
    // transButtonType: PopularizeFetchEnum.transButtonTypeEnum.缩放2,
    // transButtonType: PopularizeFetchEnum.transButtonTypeEnum.发光3,
    // transButtonType: PopularizeFetchEnum.transButtonTypeEnum.流动4,
/////// google ads 相关的修改
/////// google ads 相关的修改
    // string google投放广告 mcc 账号id  只记录埋点即可
    mccId	:  'mccId只用于埋点统计',
    // string facebook追踪器 pixelid
    // pixelId	:  '200075699619404',
    // pixelId	:  '9999999',
    // pixelId	:  '4f38757b-41e1-4918-a609-439e73c6aa78',
    pixelId: getPixelId(),
    
    // object 默认
    jumpUrlMap: {
        facebook_key1: "http://www.baidu111.com",
        facebook_key2: "http://www.baidu222.com",
    },
    // string 默认
    // jumpDefaultUrl: "https://play.google.com/store/apps/details?id=com.magfic.novel.buu",
    jumpDefaultUrl: "http://www.google.com",
}
export default e

export {
    lineStyleFilter,
    PopularizeFetchEnum
}