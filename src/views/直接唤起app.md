要实现通过链接直接唤起Android和iOS设备上已安装的App，需要结合**深度链接（Deep Link）**和**延迟深度链接（Deferred Deep Link）**技术，并通过AppsFlyer归因链接参数进行适配。以下是具体实现步骤和优化建议：

---

### 一、核心原理
1. **直接唤起条件**：
   - **Android**：通过配置`Intent Scheme`或`App Links`（需HTTPS域名验证）。
   - **iOS**：通过`Universal Links`（需苹果认证的域名和文件验证）。
   - **AppsFlyer的作用**：在归因链接中嵌入深度链接参数，并通过`af_r`参数处理未安装时的跳转逻辑。

2. **用户当前链接的问题**：
   ```diff
   https://magfic.onelink.me/oxJU?...&af_r=http%3A%2F%2Fwww.google.com
   ```
   - `af_r`指向备用URL为Google首页，未适配应用商店跳转逻辑。
   - 深度链接参数（如`deep_link_value=reading_page`）需与App内路由规则匹配。

---

### 二、Android端适配步骤
#### 1. 配置Android App Links
   - **在AndroidManifest.xml中添加Intent Filter**：
     ```xml
     <intent-filter android:autoVerify="true">
         <action android:name="android.intent.action.VIEW" />
         <category android:name="android.intent.category.DEFAULT" />
         <category android:name="android.intent.category.BROWSABLE" />
         <!-- 声明支持的域名 -->
         <data android:scheme="https"
               android:host="magfic.onelink.me"
               android:pathPrefix="/oxJU" />
     </intent-filter>
     ```
   - **在AppsFlyer后台配置Android App Links**：
     - 上传`assetlinks.json`文件到域名服务器（参考[Google官方文档](https://developers.google.com/digital-asset-links)）。

#### 2. 修改链接参数
   - **调整`af_r`备用链接**：
     ```diff
     - &af_r=http%3A%2F%2Fwww.google.com
     + &af_r=https%3A%2F%2Fplay.google.com%2Fstore%2Fapps%2Fdetails%3Fid%3Dcom.your.app.package
     ```
     当用户未安装App时，跳转到Google Play商店页面。

---

### 三、iOS端适配步骤
#### 1. 配置Universal Links
   - **在Xcode中启用Associated Domains**：
     - 添加域名：`applinks:magfic.onelink.me`。
   - **在AppsFlyer后台配置iOS Universal Links**：
     - 上传`apple-app-site-association`文件到域名服务器（需苹果认证的HTTPS域名）。

#### 2. 修改链接参数
   - **调整`af_r`备用链接**：
     ```diff
     - &af_r=http%3A%2F%2Fwww.google.com
     + &af_r=https%3A%2F%2Fapps.apple.com%2Fapp%2Fid123456789
     ```
     当用户未安装App时，跳转到App Store页面。

---

### 四、深度链接参数优化
确保App内能解析以下参数并跳转到对应页面：
```text
deep_link_value=reading_page
deep_link_sub1=27
deep_link_sub2=2
...
```
- **App端需实现**：
  - 解析URL中的参数（如通过`getIntent().getData()`（Android）或`application(_:open:options:)`（iOS））。
  - 根据`deep_link_value`等参数路由到指定页面（如`reading_page`）。

---

### 五、完整优化后的链接示例
```url
https://magfic.onelink.me/oxJU?
af_js_web=true&
af_ss_ver=2_9_2&
pid=web2app_fb&
c={{campaign.name}}&
af_ad={{ad.name}}&
af_adset={{adset.name}}&
deep_link_value=reading_page&
deep_link_sub1=27&
af_campaignid={{campaign.id}}&
deep_link_sub2=2&
deep_link_sub3=web2app_fb&
deep_link_sub4={{campaign.name}}&
deep_link_sub5={{campaign.id}}&
deep_link_sub6={{ad.name}}&
deep_link_sub7={{adset.name}}&
deep_link_sub8=0&
af_r=https%3A%2F%2Fplay.google.com%2Fstore%2Fapps%2Fdetails%3Fid%3Dcom.your.app.package&  # Android备用链接
af_ios_url=https%3A%2F%2Fapps.apple.com%2Fapp%2Fid123456789&  # iOS备用链接
af_ss_ui=true
```

---

### 六、验证与测试
1. **Android测试**：
   - 已安装App：点击链接直接打开App并跳转到`reading_page`。
   - 未安装App：跳转到Google Play商店。
2. **iOS测试**：
   - 已安装App：通过Universal Links直接唤起App。
   - 未安装App：跳转到App Store。
3. **调试工具**：
   - 使用AppsFlyer的[测试工具](https://support.appsflyer.com/hc/en-us/articles/207032126-AppsFlyer-Testing-Tool)检查归因和深度链接是否生效。

---

通过以上配置，您的链接将实现：
- **已安装App**：直接唤起并传递深度参数。
- **未安装App**：跳转到应用商店，用户安装后首次打开仍能还原深度链接场景（延迟深度链接）。