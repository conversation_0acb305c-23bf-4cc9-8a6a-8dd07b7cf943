<template>
    <div>
        <!-- <van-button size="large" @click="emialAutoLocation">跳转</van-button> -->
    </div>
</template>

<script>
    import { mapState } from "vuex"
    import VueClipBoard from "vue-clipboard2"
    import { Toast } from "vant";
    import mainUtils from "@/utils/mainUtils.js"
    import Vue from "vue"
    Vue.use(VueClipBoard)

    const EmailTrackEvents = {
        act_btn_click: "act_btn_click",
    }
    class EmailOnlinke {
        static emailPidValue = 'web_activity'
        static emailImportStatus = false
        static emailImportJs() {
            try {
                // ！！ 注意这里魔法字符串不能带前缀的 @ 因为会解析不到
                require(`@/${GloablEnvConfig.EMAIL_FILENAME}`)
                
                // 判断是否成功引入文件
                if (window.AF_SMART_SCRIPT?.generateOneLinkURL instanceof Function) {
                    this.emailImportStatus = true
                    console.log(`【js Import】 email JS 创建调用成功`)
                } else {
                    throw new Error('AF_SMART_SCRIPT 对象或 generateOneLinkURL 方法未定义')
                }
            } catch (error) {
                console.error(`【js Import】 email 引入 js 失败：`, error)
                this.emailImportStatus = false
            }
        }
        static emialUrlParams = {
            page_path: 'detail',
            page_value: '10086',
            packageName: 'com.magfic.novel.buu',
            source: 'web_123activity'
        }
        static emialUrlCheck(query) {
            const requiredParams = Object.keys(this.emialUrlParams)
            const missingParams = requiredParams.filter(param => !query[param])
            
            if (missingParams.length > 0) {
                Toast({
                    // message: `缺少必要参数: ${missingParams.join(', ')}`,
                    message: `Error: Missing required \n parameters `, // 
                    duration: 0,
                })
                return false
            }
            return true
        }
    
        // 添加URL参数拼接方法
        static buildUrl(baseUrl, params) {
            const url = new URL(baseUrl);
            Object.entries(params).forEach(([key, value]) => {
                url.searchParams.set(key, value);
            });
            return url.href;
        }
        
        // 使用示例：
        static lookExampleUsage() {
            const baseUrl = 'http://localhost:8080/deepchain'
            const fullUrl = this.buildUrl(baseUrl, EmailOnlinke.emialUrlParams)
            console.log(fullUrl)
            // 输出: http://localhost:8080/deepchain?page_path=detail&page_value=10086&packageName=com.magfic.novel.buu&source=web_123activity
            // 输出: http://localhost:8080/deepchain?page_path=detail
        }
    }
    export default {
        name: "deepchain",
        computed: {
            // 数数设备信息
            ...mapState("user", ["shushuDeviceMsg"]),
        },

        data() {
            return {
                oneLinkURLGO: "",
                locationHrefModelOpen: true,
            }
        },
        watch: {
        
        },
        methods: {
            // 获取路由参数
            getRouteQuery() {
                return structuredClone(this.$route.query)
            },
            getOnlinePidValue() {
                let query = this.getRouteQuery()
                return query.source
            },
            // 生成 email onlinke 链接
            createOnlinkeOfemail() {
                var mediaSource = {
                    keys: [
                        "source"
                    ],
                    defaultValue:"web_activity"
                }
                var deepLinkValue = {
                    keys:["page_path"]
                }
                var deep_link_sub1 = {
                    paramKey:"deep_link_sub1",
                    keys:["page_value"]
                }
                var custom_ss_ui = {
                    paramKey:"af_ss_ui",
                    defaultValue:"true"
                }

                let params = {
                    oneLinkURL: window.GloablEnvConfig.oneLinkURL,
                    afParameters: {
                        mediaSource: mediaSource,
                        deepLinkValue: deepLinkValue,
                        afCustom: [
                            deep_link_sub1,
                            custom_ss_ui
                        ]
                    }
                }
                
                let result = window.AF_SMART_SCRIPT.generateOneLinkURL(params)
                let result_url = "No output from script"
                if (!result) {
                    Toast("生成归因链接失败，请检查格式")
                    return
                }

                result_url = result.clickURL
                console.log('\n\n')
                console.error("【生成归因链接是】: ========================")
                console.log("【生成归因链接是】: ~ 1 result_url 之前:", result_url)

                // #region 因为会无缘无故 pid=fb 按正常来说 pid=url上的source的值，现在这里直接强制重写
                let newUrl = new URL(result_url)
                let paramSourceValue = this.getOnlinePidValue() // 获取拿 result_url.searchParams.get('deep_link_sub3') 因为和 url上的source 值是一样的
                newUrl.searchParams.set('pid', paramSourceValue)
                result_url = newUrl.href
                // #endregion

                console.log("【生成归因链接是】: ~ 2 result_url 之后:", result_url)
                console.log("【生成归因链接是】: ~ 3 result_url 最终:", result_url)
                console.error("【生成归因链接是】: ========================\n")
                console.log('\n\n')

                //将OneLink智能脚本生成的URL赋值this.oneLinkURLGO  跳转时用到
                this.oneLinkURLGO = result_url
                //https://afbasicapp.onelink.me/CJRS?af_js_web=true&af_ss_ver=2_2_0&pid=default_media_source&c=default_campaign&af_ss_ui=true
            },
            // 邮箱打点
            emailPoint() {
                let query = this.getRouteQuery()
                let page_path = query.page_path
                let page_value = query.page_value
                let source = this.getOnlinePidValue()
                let pointData = {
                    page_path: page_path,
                    page_value: page_value,
                    source: source,
                }
                this.$ta.track(EmailTrackEvents.act_btn_click, pointData)
            },
            // 复制参数内容
            async useCopyResult() {
                // try {
                //     if (navigator && navigator.clipboard && navigator.clipboard.writeText) {
                //         await navigator.clipboard.writeText(this.clipboardTxt);
                //         Toast('复制成功1')
                //     } else {
                //         throw new Error('浏览器不支持 clipboard')
                //     }
                // } catch (err) {
                //     Toast('复制失败1')
                //     console.error('复制失败:', err);
                // }

                
                // const tempInput = document.createElement('input');
                // tempInput.style.position = 'absolute';
                // tempInput.style.left = '-9999px';
                // document.body.appendChild(tempInput);
                // tempInput.value = this.clipboardTxt;
                // tempInput.select();
                // try {
                //     document.execCommand('copy');
                //     Toast('复制成功2')
                // } catch (err) {
                //     Toast('复制成功2')
                // }
                // document.body.removeChild(tempInput);

                
                let { text } = await this.$copyText(this.clipboardTxt)
                console.log('\n\n')
                console.log("【使复制内容】: ~  text:", text)
                console.log('\n\n')
            },
            // 创建复制内容
            createCopyResult() {
                let result = this.getRouteQuery()
                this.clipboardTxt = JSON.stringify(result)
                console.log('\n\n')
                console.log("【生成复制内容】: ~  clipboardTxt:", this.clipboardTxt)
                console.log('\n\n')
            },
            // 邮箱自动跳转
            async emialAutoLocation() {
                // this.createCopyResult()
                // await this.useCopyResult()
                // 打点
                this.emailPoint()
                console.log("【即将跳转】: ~ 即将跳转", this.oneLinkURLGO)
                // 睡眠500ms 否则无法打点
                let locationBeforeDebug = window.GloablEnvConfig.isDevScript || this.getRouteQuery().closeAppstoreLocation
                if (locationBeforeDebug) {
                    await this.$dialog.confirm({
                        title: '是否跳转到商场的onlinke页面',
                        message: `
                        当前剪贴板copy内容：${this.clipboardTxt}\n
                        即将跳转的online链接：${this.oneLinkURLGO}\n
                        `,
                    })
                }
                mainUtils.sleep(500)
                // 跳转
                window.location.href = this.oneLinkURLGO
            },
            __init() {
                // this.createCopyResult()

                EmailOnlinke.emailImportJs()
                if (!EmailOnlinke.emailImportStatus) {
                    Toast("引入JS失败")
                    return
                }

                this.createOnlinkeOfemail()
                if (!this.oneLinkURLGO) {
                    Toast("生成归因链接失败")
                    return
                }

                this.emialAutoLocation()
            },
            
        },

        created() {
            EmailOnlinke.lookExampleUsage()
            if (!EmailOnlinke.emialUrlCheck(this.getRouteQuery())) {
                return
            }
            this.__init()
        },
        mounted() {
        },
        beforeDestroy() {
        },
        beforeRouteLeave(to, from, next) {
            next()
        }
    }
</script>
<style>

</style>
<style lang="scss" scoped>
</style>
