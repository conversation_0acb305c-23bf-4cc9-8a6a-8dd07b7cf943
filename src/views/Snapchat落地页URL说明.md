# Snapchat 落地页 URL 说明

## 测试环境 (Test Environment)

**URL:** `https://test-magfic-web-us.unbing.cn/popularize?testSecretKey=kvBvawSSWrntdmZp&packageName=com.magfic.novel.buu&channel=snapchat_ip_int&source=null&cpm=9j5SywZ2swvS1qdwcMGxDQ&book_id=4155&book_version=0&book_version_name=origin&chapter_id=3&pixelId=99999&utm_campaign={{campaign.name}}&utm_campaign_id={{campaign.id}}&utm_adid={{ad.id}}&utm_adset={{adSet.name}}&utm_adset_id={{adSet.id}}`

**参数 (Parameters):**

```json
{
    "book_id": "4155",
    "book_version": "0",
    "book_version_name": "origin",
    "channel": "snapchat_ip_int",
    "chapter_id": "3",
    "cpm": "9j5SywZ2swvS1qdwcMGxDQ",
    "packageName": "com.magfic.novel.buu",
    "pixelId": "99999",
    "source": "null",
    "testSecretKey": "kvBvawSSWrntdmZp",
    "utm_adid": "{{ad.id}}",
    "utm_adset": "{{adSet.name}}",
    "utm_adset_id": "{{adSet.id}}",
    "utm_campaign": "{{campaign.name}}",
    "utm_campaign_id": "{{campaign.id}}"
}
```

## 正式环境 (Production Environment)

(待补充)

## URL 参数说明 (URL Parameter Explanations)

### 动态参数 (Dynamic Parameters)
Snapchat 广告支持动态URL参数，这些参数会在广告被点击时自动替换为相应的值。这对于追踪广告活动的效果非常有用。

| 参数 | 描述 |
| --- | --- |
| `{{campaign.name}}` | 广告系列的名称 |
| `{{campaign.id}}` | 广告系列的唯一ID |
| `{{adSet.name}}` | 广告组的名称 (Ad Set Name) |
| `{{adSet.id}}` | 广告组的唯一ID (Ad Set ID) |
| `{{ad.id}}` | 广告的唯一ID (Ad ID) |


### 自定义参数 (Custom Parameters)
这些是为本应用定制的参数，用于内部追踪和逻辑处理。

| 参数 | 描述 |
| --- | --- |
| `testSecretKey` | 用于测试环境的密钥。 |
| `packageName` | 应用的包名。 |
| `channel` | 渠道来源。 |
| `source` | 流量来源。 |
| `cpm` | 一个加密的市场推广参数。 |
| `book_id` | 书籍ID。 |
| `book_version` | 书籍版本。 |
| `book_version_name` | 书籍版本名称。 |
| `chapter_id` | 章节ID。 |
| `pixelId` | Snapchat Pixel ID，用于追踪网站上的用户行为。 |
