# Facebook 落地页 URL 说明

## 测试环境 (Test Environment)

**URL:** `https://test-magfic-web-us.unbing.cn/popularize?testSecretKey=kvBvawSSWrntdmZp&packageName=com.magfic.novel.buu&channel=facebook_ip_int&source=web2app_fb&cpm=RFAeSnLajls5hnzrs9ewEA&book_id=4626&book_version=0&book_version_name=origin&chapter_id=4&pixelId=569249758843694&utm_campaign={{campaign.name}}&utm_campaignid={{campaign.id}}&utm_ad={{ad.name}}&utm_adset={{adset.name}}`

**参数 (Parameters):**

```json
{
  "book_id": "4626",
  "book_version": "0",
  "book_version_name": "origin",
  "channel": "facebook_ip_int",
  "chapter_id": "4",
  "cpm": "RFAeSnLajls5hnzrs9ewEA",
  "packageName": "com.magfic.novel.buu",
  "pixelId": "569249758843694",
  "source": "web2app_fb",
  "testSecretKey": "kvBvawSSWrntdmZp",
  "utm_ad": "{{ad.name}}",
  "utm_adset": "{{adset.name}}",
  "utm_campaign": "{{campaign.name}}",
  "utm_campaignid": "{{campaign.id}}"
}
```

## 正式环境 (Production Environment)

**URL:** `https://web.magfic.com/popularize?packageName=com.magfic.novel.buu&channel=facebook_ip_int&source=web2app_fb&cpm=WfAEwBzEZXBBQ41Pq_AhtA&book_id=1000851&book_version=0&book_version_name=origin&chapter_id=4&pixelId=200075699619404&utm_campaign=unBing_magfic_valuecountry_bid_1000026_aeo_my2_test&utm_campaignid=cid0817test001&utm_ad=ad0817test001&utm_adset=adset0817test001&fbclid=IwAR07OckEQflipAo89MYbhKTmuQcIw6bbOsyUzDaQhR9czyd1u1QE8bbW0Dw_aem_AYzRNhvoin4qefevj37P_8TUI5TR5oQnxzDtmdglhxSaO9lvANT0jrDNzKd7QQKb74Akw-_-8sLi5jYk65wu5gpp&af_r=https%3A%2F%2Fplay.google.com%2Fstore%2Fapps%2Fdetails%3Fid%3Dcom.magfic.novel.buu`

**参数 (Parameters):**

```json
{
  "af_r": "https://play.google.com/store/apps/details?id=com.magfic.novel.buu",
  "book_id": "1000851",
  "book_version": "0",
  "book_version_name": "origin",
  "channel": "facebook_ip_int",
  "chapter_id": "4",
  "cpm": "WfAEwBzEZXBBQ41Pq_AhtA",
  "fbclid": "IwAR07OckEQflipAo89MYbhKTmuQcIw6bbOsyUzDaQhR9czyd1u1QE8bbW0Dw_aem_AYzRNhvoin4qefevj37P_8TUI5TR5oQnxzDtmdglhxSaO9lvANT0jrDNzKd7QQKb74Akw-_-8sLi5jYk65wu5gpp",
  "packageName": "com.magfic.novel.buu",
  "pixelId": "200075699619404",
  "source": "web2app_fb",
  "utm_ad": "ad0817test001",
  "utm_adset": "adset0817test001",
  "utm_campaign": "unBing_magfic_valuecountry_bid_1000026_aeo_my2_test",
  "utm_campaignid": "cid0817test001"
}
```

## URL 参数说明 (URL Parameter Explanations)

### 动态参数 (Dynamic Parameters)
Facebook 广告支持动态URL参数，这些参数会在广告被点击时自动替换为相应的值。这对于追踪广告活动的效果非常有用。

| 参数 | 描述 |
| --- | --- |
| `{{campaign.name}}` | 广告系列的名称 |
| `{{adset.name}}` | 广告组的名称 |
| `{{ad.name}}` | 广告的名称 |
| `{{campaign.id}}` | 广告系列的唯一ID |
| `{{adset.id}}` | 广告组的唯一ID |
| `{{ad.id}}` | 广告的唯一ID |

**注意:** `{{campaign.name}}`, `{{adset.name}}`, 和 `{{ad.name}}` 会使用广告首次发布时的名称。如果你后续编辑了名称，该参数仍然会返回原始名称。

### Facebook 特定参数 (Facebook-Specific Parameters)
| 参数 | 描述 |
| --- | --- |
| `fbclid` | Facebook Click Identifier。当用户点击Facebook上的链接时，Facebook会自动附加此参数，用于追踪和归因。 |

### AppsFlyer 参数 (AppsFlyer Parameters)
| 参数 | 描述 |
| --- | --- |
| `af_r` | 用于将用户重定向到应用商店的URL。这通常由第三方移动归因平台（如 AppsFlyer）使用。 |

### 自定义参数 (Custom Parameters)
这些是为本应用定制的参数，用于内部追踪和逻辑处理。

| 参数 | 描述 |
| --- | --- |
| `testSecretKey` | 用于测试环境的密钥。 |
| `packageName` | 应用的包名。 |
| `channel` | 渠道来源。 |
| `source` | 流量来源。 |
| `cpm` | 一个加密的市场推广参数。 |
| `book_id` | 书籍ID。 |
| `book_version` | 书籍版本。 |
| `book_version_name` | 书籍版本名称。 |
| `chapter_id` | 章节ID。 |
| `pixelId` | Facebook Pixel ID，用于追踪网站上的用户行为。 |