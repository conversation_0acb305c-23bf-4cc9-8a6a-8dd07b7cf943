### Snapchat 广告渠道接入 - 核心需求

**目标**: 在 `ChannelChange.js` 中集成 Snapchat 广告跟踪。

---

#### 1. 关键信息

*   **渠道 (Channel) URL 参数**: `channel=snapchat_ip_int`
*   **像素 ID (Pixel ID)**: `888`
*   **跟踪事件 (Track Event)**:
    *   `PAGE_VIEW`: 页面加载时触发。
    *   `START_CHECKOUT`: 用户点击下载按钮时触发。
    *   `VIEW_CONTENT`: 用户滚动到底部时触发。

---

#### 2. Snapchat Pixel 脚本

**初始化脚本 (Base Code):**
```javascript
(function(e,t,n){if(e.snaptr)return;var a=e.snaptr=function()
{a.handleRequest?a.handleRequest.apply(a,arguments):a.queue.push(arguments)};
a.queue=[];var s='script';r=t.createElement(s);r.async=!0;
r.src=n;var u=t.getElementsByTagName(s)[0];
u.parentNode.insertBefore(r,u);})(window,document,
'https://sc-static.net/scevent.min.js');
```

**调用逻辑:**
1.  `snaptr('init', 'YOUR_PIXEL_ID');`
2.  `snaptr('track', 'EVENT_NAME');`

---

#### 3. `ChannelChange.js` 修改点

**A. 新增静态属性:**
在类的顶部，添加 Snapchat 相关的状态变量和事件映射。
```javascript
static isSnapchat = false;
static snapchatResult = null;
static snapchatTrackMap = new Map([
    ['PageView', 'PAGE_VIEW'],
    ['ClickButton', 'START_CHECKOUT'],
    ['ViewContentFinish', 'VIEW_CONTENT'],
]);
```

**B. 新增初始化方法:**
在类中，仿照 `tiktokPixedlIdInit` 添加 `snapchatPixedlIdInit` 方法。
```javascript
static snapchatPixedlIdInit() {
    // 粘贴上面的 "初始化脚本 (Base Code)"
    (function(e,t,n){...})(window,document,'https://sc-static.net/scevent.min.js');

    // 初始化并上报页面浏览事件
    snaptr('init', this.currentPixelId);
    this.snapchatResult = snaptr;
    this.trackEvent(this.snapchatTrackMap.get('PageView'));
}
```

**C. 修改现有方法:**
1.  **`setSource(channel)`**: 添加 `else if` 来识别 `snapchat_ip_int`。
2.  **`autoImportChanelJs(pixelId)`**: 添加 `else if`，当 `this.isSnapchat` 为 `true` 时，调用 `this.publicPixedlIdInit(pixelId)`。
3.  **`publicPixedlIdInit(pixelId)`**: 添加 `else if`，当 `this.isSnapchat` 为 `true` 时，调用 `this.snapchatPixedlIdInit()`。
4.  **`trackEvent(eventName, eventData)`**: 添加 `else if`，当 `this.isSnapchat` 为 `true` 时，调用 `this.snapchatResult('track', eventName, eventData)`。
5.  **`buttonChangleTrack()`**: 添加 `if (this.isSnapchat)` 分支，调用 `this.trackEvent(this.snapchatTrackMap.get('ClickButton'))`。
6.  **`viewContentFinishChangleTrack()`**: 添加 `if (this.isSnapchat)` 分支，调用 `this.trackEvent(this.snapchatTrackMap.get('ViewContentFinish'))`。

---

#### 4. 参考文档

*   **Snapchat Pixel 事件与脚本**: [https://businesshelp.snapchat.com/s/article/view-content-event?language=zh_CN](https://businesshelp.snapchat.com/s/article/view-content-event?language=zh_CN)
*   **Snapchat Cookie 信息**: [https://www.snap.com/privacy/cookie-information](https://www.snap.com/privacy/cookie-information)
*   **Snapchat URL 宏说明**: [https://businesshelp.snapchat.com/s/article/add-url-macros?language=zh_CN](https://businesshelp.snapchat.com/s/article/add-url-macros?language=zh_CN)
*   **公司内部接口对接文档**: [https://alidocs.dingtalk.com/i/nodes/2Amq4vjg89RXzEvQCd2ArEd2W3kdP0wQ](https://alidocs.dingtalk.com/i/nodes/2Amq4vjg89RXzEvQCd2ArEd2W3kdP0wQ)