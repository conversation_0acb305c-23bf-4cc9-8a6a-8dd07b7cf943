import axios from "axios";
import <PERSON><PERSON> from "@/utils/cookie.js";
import store from "@/store/index.js";
import {anonymousLogin} from "@/utils/commonUtil.js";
import * as nameConfig from '~/sassConfig/nameConfig.js'
import {EncryptSHA512,EncryptAES,DecryptAES} from '@/utils/encryption.js'

import _ from 'lodash'

let baseURL = GloablEnvConfig.VUE_APP_BASE_URL
if (nameConfig.currentUploadSeverPath) {
    baseURL = nameConfig.currentUploadSeverPath.slice(0, -1) // 原本是 /a/ 去掉最后一个 /
}
// console.log("🚀 ~ baseURL:", baseURL)

const CancelToken = axios.CancelToken;

axios.interceptors.request.use(
  config => {
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);

axios.interceptors.response.use(
  response => {
    return response;
  },
  error => {
    return Promise.resolve(error.response);
  }
);

const toErrorPage = () => {
  if (window.GloablEnvConfig.isBuildScript) {
    window.location.href=  window.location.origin + '/error'
  }
}

const httpServer = (opts, data) => {
  let url = "";
  let method = "post";
  if (typeof opts === "string") {
    url = opts;
  } else {
    url = opts.url;
    method = opts.method || "post";
  }

  let authorization = "";
  if (store.state.user.token !== "") {
    authorization = `${store.state.user.token}`;
  }
  // http默认配置
  let httpDefaultOpts = {
    method: method,
    baseURL,
    url: url,
    timeout: 20000,
    data: EncryptAES(JSON.stringify(Object.assign(data))),
  };
  if (opts.cancelToken) {
    httpDefaultOpts.cancelToken = new CancelToken(function executor(c) {
      opts.cancelToken(c);
    });
  }
  const notice=Math.floor(Math.random()*900)+100
  const locale='en_US'
  const time=Date.now()

  if (httpDefaultOpts.method === "get" || httpDefaultOpts.method === "put") {
    httpDefaultOpts.params =  EncryptAES(Object.assign(data));
    httpDefaultOpts.headers = {
      token: authorization,
      locale,
      notice,
      time,
      sign:EncryptSHA512(notice,time)
    };
  } else {
    httpDefaultOpts.headers = {
      token: authorization,
      "Content-Type": "application/json",
      locale,
      notice,
      time,
      sign:EncryptSHA512(notice,time)
    };
  }
  if(Cookie.get('testSecretKey')!==''){
    httpDefaultOpts.headers.testSecretKey=Cookie.get('testSecretKey')
  }


  let promise = new Promise(function (resolve, reject) {
    axios(httpDefaultOpts)
      .then(res => {
        if(res.data.data){
          try {
            if(_.split(DecryptAES(res.data.data), '1')[0]==''){
              res.data.data=DecryptAES(res.data.data)
            }else{
              const orginData=_.split(DecryptAES(res.data.data), '}')
              orginData.pop()
              let clearNull=orginData.join('}')
                if(clearNull[0]=='['){
                  clearNull=clearNull+'}]'
                }else{
                  clearNull=clearNull+'}'
                }

                res.data.data=eval("("+clearNull+")")
            }
          } catch (error) {
            const orginData=_.split(DecryptAES(res.data.data), ']')
            orginData.pop()
            let clearNull=orginData.join(']')+']'
              console.log("error:",clearNull);
            res.data.data=eval(clearNull)
          }
        }
        if (!res) {
            console.log("res is error");
          reject(
            new Error({
              error_message:
                "Connect erro, check your network and try again later.",
            })
          );
          return false;
        }
        if (res.status === 403) {
          let routerUrl=httpDefaultOpts.url;
          var startRes = routerUrl.startsWith("api/web") ?true:false;
          if(undefined !=startRes && startRes){

              console.log("403 msg:",res.msg);
              store.dispatch('user/setToken', '');
              // window.location.reload();
              // anonymousLogin();
              return false;

          }else{
              console.log("403 msg:",res.msg);
             return false;
        }
       }else if (res.status === 401) {
          console.log("msg:",res.msg);
            debugger;
          if(undefined !=startRes && startRes){
            //  window.location.href=  window.location.origin+'/error';
            toErrorPage()
          }
          return false;
        }else if(res.status !=200){
          reject('Internal Server Error');
         // window.location.href=  window.location.origin+'/error';
           return false;
        }

        const data = res.data;

        if (data.code == "500") {
          reject('Internal Server Error');
          return false;
        }else if(data.code =="700"){
          reject('Invalid Link');
          // window.location.href=  window.location.origin+'/error';
          toErrorPage()
        }else if(data.code !=0){
            console.log("msg:",data.msg);
            reject(data.msg);
            // window.location.href=  window.location.origin+'/error';
            return false;
        }

        if (!data.data) {
          reject(data.msg);
        } else {
          resolve(data.data);
        }
      })
      .catch(response => {
        reject(response);
      });
  });
  return promise;
};
export default httpServer;
