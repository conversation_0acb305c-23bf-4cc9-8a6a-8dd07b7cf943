<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-02-02 15:44:19
 * @LastEditors: chen<PERSON><PERSON>
 * @Description: 
 * @LastEditTime: 2023-02-02 16:26:58
-->

<template>
  <div class="wrapper-title-bar">
    <img
      class="wrapper-back"
      src="@/img/img_public/common/back-day.png"
      alt=""
      srcset=""
    />
    <span class="wrapper-title">{{ title }}</span>
  </div>
</template>

<script>
export default {
  //import引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    title: {
      type: String,
      require: true,
    },
  },
  data() {
    return {};
  },
  computed: {},
  watch: {},
  methods: {
    back() {
      this.$router.back();
    },
  },
  created() {},
  mounted() {},
};
</script>
<style lang='scss' scoped>
//@import url(); 引入公共css类
.wrapper-title-bar {
  width: 100%;
  height: 117px;
  background: #f9f9f9;
  position: relative;
  position: fixed;
  top: 0;
  border: 0;
  .wrapper-back {
    width: 63px;
    height: 63px;
    position: absolute;
    top: 27px;
    left: 33px;
  }
  .wrapper-title {
    width: 100%;
    height: 117px;
    line-height: 117px;
    display: inline-block;
    text-align: center;
    font-size: 29px;
    font-family: Poppins-Medium, Poppins;
    font-weight: 500;
    color: #252b37;
  }
}
</style>