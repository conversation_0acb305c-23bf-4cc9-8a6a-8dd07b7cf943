<template>
    <div class="sub">
        <div class="subLottieRef"></div>
    </div>
</template>

<style scoped lang="scss">
.sub {
    // background: red;
    width: 100%;
    height: 100%;
    .subLottieContainer {
        width: 100%;
        height: 100%;
        img {
            width: 100%;
            height: 100%;
        }
        .subLottieRef {
            width: 100%;
            height: 100%;
        }
    }
}
</style>
<script>
import lottie from 'lottie-web'
import lodash from "lodash"

export default {
    data() {
        return {
        }
    },
    methods: {
        initLottie() {
            lottie.destroy() // https://github.com/airbnb/lottie-web 先销毁所有的lottie动画 否则会有多个DOM生成
            // let lottiePath = `img/img_public/会员订阅制/按钮动画`
            let lottiePath = `img/img_public/手指`
            let animationData = lodash.cloneDeep(require(`../${lottiePath}/data.json`))
            animationData.assets.forEach((e) => {
                // 这里需要重新加载图片路径，否则会找不到图片
                if (e.w && e.h) {
                    e.u = ''
                    e.p = require(`../${lottiePath}/images/${e.p}`)
                }
            })
            console.log("🚀 ~ animationData.assets.forEach ~ animationData:", animationData)
            lottie.loadAnimation({
                container: document.querySelector(".subLottieRef"), // 因为使用了for，所以用querySelector
                renderer: 'svg',
                loop: true,　// 是否循环
                autoplay: true,// 是否自动播放
                animationData: animationData, // 加载动画数据
            })
        },
        async __pageInit() {
            setTimeout(() => {
                this.initLottie()
            }, 0)
        },
        __init() {
            this.__pageInit()
        }
    },

    created() {
        this.__init()
    },

    mounted() {
    },

    beforeDestroy() {
    }
}

</script>