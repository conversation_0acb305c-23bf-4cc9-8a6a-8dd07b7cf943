const webpack = require('webpack')
const { merge } = require('webpack-merge')
const SpritesmithPlugin = require("webpack-spritesmith");
const path = require('path')

// const CustomLoader = path.resolve(__dirname, 'webpackCustom/custom-loader.js')
// const CustomPlugin = require(path.resolve(__dirname, 'webpackCustom/custom-plugin.js'))

// 选择开发环境的配置
function seleConfig() {
  // 普通
  const local= require("./vue.config.local.js");
  const dev = require("./vue.config.dev.js");
  const prod = require("./vue.config.prod.js");
  const development_overseas = require("./vue.config.dev-overseas.js");
  if( process.env.NODE_ENV === "local"){
	  return local;
  }else if(process.env.NODE_ENV === "development"){
	  return dev;
  }else if(process.env.NODE_ENV === "development-overseas"){
	  return development_overseas;
  }else{
	 return prod;
  }  
}
const config = seleConfig()
// 生成时间戳用于文件名，确保每次构建产生不同的文件名
const Timestamp = new Date().getTime();
let publicPath = './' //默认路径
if(JSON.stringify(process.env.PROJECT_SERVER_PATH)){
  publicPath = `/${process.env.PROJECT_SERVER_PATH}/`
}
console.log("🚀 ~ process.env.PROJECT_SERVER_PATH:", process.env.PROJECT_SERVER_PATH)
console.log("🚀 ~ process.env.PROJECT_NAME:", process.env.PROJECT_NAME)
console.log("🚀 ~ process.env.PROJECT_ENV:", process.env.PROJECT_ENV)
console.log("🚀 ~ process.env.PROJECT_SCRIPT:", process.env.PROJECT_SCRIPT)
console.log("🚀 ~ BUILD_TIMESTAMP:", Timestamp)
const defaultConfig = {
  productionSourceMap: false,
  // publicPath: './', // 设置公共路径
  publicPath: publicPath,
  devServer: {
        // port: 8090,
        host: '0.0.0.0',
        https: true,
        // https: false,
        // open: true
        
        // https://cloud.tencent.com/developer/article/2067215
        disableHostCheck: true, // 让 域名 + localhost + 127.0.0.1 都可访问
        
        // webpack-dev-server 3.x版本兼容配置
        allowedHosts: [
          'localhost',
          '127.0.0.1',
          'linzengrui-dev.unbing.cn',
        ],
        
        // 解决sockjs CORS问题的简化配置
        // sockHost: 'localhost', // 默认使用localhost，避免跨域
        // sockPort: 'auto',      // 自动检测端口
        
        // 动态配置public地址（支持多种访问方式）
        // public: process.env.DEV_HOST ? `${process.env.DEV_HOST}:8080` : 'localhost:8080',
        public: 'linzengrui-dev.unbing.cn:8080', // 固定使用域名，强制sockjs使用此地址
        
        // 重新启用热更新
        hot: true,         // 启用热更新
        liveReload: true,  // 启用实时重载
        
        // 额外的热更新配置
        inline: true,      // 启用内联模式
        overlay: true,     // 显示编译错误覆盖层   
  },
  // devServer: {
  //   contentBase: path.join(__dirname, 'public'),
  // },
  // svga特殊处理
  configureWebpack: {
    plugins: [
      new webpack.DefinePlugin({
        // 项目名
        'process.env.PROJECT_NAME': JSON.stringify(process.env.PROJECT_NAME),
        // 项目环境
        'process.env.PROJECT_ENV': JSON.stringify(process.env.PROJECT_ENV),
        // 项目模式 :  开发 devScript  打包 buildScript
        'process.env.PROJECT_SCRIPT': JSON.stringify(process.env.PROJECT_SCRIPT),
        // 项目服务器路径
        'process.env.PROJECT_SERVER_PATH': JSON.stringify(process.env.PROJECT_SERVER_PATH),
        // 构建时间戳
        'process.env.BUILD_TIMESTAMP': JSON.stringify(Timestamp),
      }),
      // new CustomPlugin({
      //   msg: '你好我是梨花酱' // 传入的插件配置  
      // })
    ],
    output: { // 输出重构 打包编译后的 文件名称 【模块名称.版本号.时间戳】
      // filename: `static/js/[name].${process.env.VUE_APP_Version}.${Timestamp}.js`,
      // 出口文件名称
      // chunkFilename: `static/js/[name].${process.env.VUE_APP_Version}.${Timestamp}.js`,

      // filename: 'bundle.js',
      
      // 使用时间戳作为文件名的一部分，确保每次构建产生不同的文件名
      filename: `js/[name].${Timestamp}.[hash:8].js`,
      chunkFilename: `js/[name].${Timestamp}.[hash:8].js`
    },
    module: {
      // rules: [
      //   // 按照图片的原始路径打包
      //   {
      //     test: /\.(png|jpg|gif|ico)$/,
      //     use: [
      //       {
      //         loader: 'file-loader',
      //         options: {
      //           // name: '[path][name].[ext]',
      //           name: 'img/[name].[hash:8].[ext]',
      //           context: 'src'
      //         }
      //       }
      //     ]
      //   }
      // ]
      rules: [  
          // {
          //     test: /\.txt$/,        
          //     // use: ['demo-loader'],
          //     use: [
          //       {
          //         loader: CustomLoader, // 使用自定义Loader
          //         options: {            
          //             name: '梨花酱', // 将要变更的通过配置项传入        
          //         }
          //       }
          //     ]
          // }
      ],
    },
  },
  chainWebpack: config => {
    /**
     * ------------- 按照图片原始路径打包 start ------------- 
     */
    // 获取默认的 file-loader 配置
    const fileLoaderRule = config.module.rule('images').test(/\.(png|jpe?g|gif|webp|ico)(\?.*)?$/);
    // 清除默认配置
    // 如果不清除的话，下面的 自定义 file loader 配置会访问不到图片，艹了
    fileLoaderRule.uses.clear();
    // 添加自定义的 file-loader 配置
    fileLoaderRule
      .use('file-loader')
      .loader('file-loader')
      .tap(options => {
        options = {
          ...options,
          // 为图片也添加时间戳
          name: `[path][name].${Timestamp}.[hash:8].[ext]`, // 输出文件名
          esModule: false, // 默认是true，打包后路径会变成绝对路径
          context: 'src'// 解析src下的东西，并且不要把src路径打包带上
        };
        return options;
      });
      /**
     * ------------- 按照图片原始路径打包 end ------------- 
     */

    // 添加raw-loader配置 用于读取txt文件
      config.module
        .rule('txt')
        .test(/\.txt$/)
        .use('raw-loader')
        .loader('raw-loader')
        .end()
      
    // 修复HMR
    config.resolve.symlinks(true)
    config.resolve.alias
      .set('@imgs', '@/images')           // 图片
      .set('@cps', '@/components')        // 通用组件
      .set('@utils', '@/utils')           // 工具方法
      .set('@v', '@/views')               // 视图
      .set('@apis', '@/apis')       // 接口
      .set('~', path.join(__dirname,))           // 接口
  },
}
// module.exports = merge(config, defaultConfig)
module.exports = defaultConfig